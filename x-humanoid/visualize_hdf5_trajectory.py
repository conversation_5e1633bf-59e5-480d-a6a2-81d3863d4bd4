#!/usr/bin/env python3
"""
HDF5轨迹数据3子图可视化分析工具
为每个episode生成包含XY轨迹、动作分量和动作幅度的综合分析图
"""

import h5py
import numpy as np
import matplotlib.pyplot as plt
import matplotlib.patches as patches
from pathlib import Path
import argparse
import seaborn as sns

# 设置专业配色方案
plt.style.use('seaborn-v0_8-whitegrid')
sns.set_palette("husl")

class TrajectoryVisualizer:
    def __init__(self):
        """初始化轨迹可视化器"""
        self.colors = {
            'x': '#1f77b4',  # 蓝色
            'y': '#ff7f0e',  # 橙色
            'z': '#2ca02c',  # 绿色
            'start': '#2ca02c',  # 绿色
            'end': '#d62728',    # 红色
            'trajectory': '#1f77b4',  # 蓝色
            'magnitude': '#d62728'    # 红色
        }
    
    def extract_trajectory_data(self, hdf5_path):
        """从HDF5文件提取轨迹数据"""
        data = {}
        
        try:
            with h5py.File(hdf5_path, 'r') as f:
                # 提取末端执行器位置数据（优先使用）
                if 'puppet/end_effector' in f:
                    end_effector = f['puppet/end_effector'][:]
                    data['positions'] = end_effector[:, :3]  # 前3维是位置
                    data['orientations'] = end_effector[:, 3:6] if end_effector.shape[1] >= 6 else None
                    data['action_type'] = 'end_effector'
                
                # 如果没有末端执行器数据，使用puppet关节位置
                elif 'puppet/joint_position' in f:
                    puppet_joints = f['puppet/joint_position'][:]
                    # 使用前3个关节作为位置代理
                    data['positions'] = puppet_joints[:, :3]
                    data['action_type'] = 'puppet_joints'
                
                # 如果都没有，使用master关节位置
                elif 'master/joint_position' in f:
                    master_joints = f['master/joint_position'][:]
                    data['positions'] = master_joints[:, :3]
                    data['action_type'] = 'master_joints'
                
                else:
                    raise ValueError("未找到可用的位置数据")
                
                # 提取语言指令
                if 'language_raw' in f:
                    instruction = f['language_raw'][:]
                    if len(instruction) > 0:
                        instruction = instruction[0]
                        if isinstance(instruction, bytes):
                            instruction = instruction.decode('utf-8')
                        data['instruction'] = instruction
                
                # 计算时间步
                data['time_steps'] = np.arange(len(data['positions']))
                data['trajectory_length'] = len(data['positions'])
                
                # 计算动作幅度（L2范数）
                data['action_magnitude'] = np.linalg.norm(data['positions'], axis=1)
                
        except Exception as e:
            print(f"❌ 提取数据失败: {e}")
            raise
        
        return data
    

    
    def create_visualization(self, hdf5_path, output_path=None, show_plot=False):
        """创建3子图可视化"""
        # 提取数据
        data = self.extract_trajectory_data(hdf5_path)

        # 获取episode ID
        episode_id = Path(hdf5_path).parent.parent.name

        # 创建图形 - 改为1行3列布局
        fig, (ax1, ax2, ax3) = plt.subplots(1, 3, figsize=(18, 6))
        fig.suptitle(f'Episode {episode_id} Action Analysis', fontsize=16, fontweight='bold')

        # 1. XY轨迹图（左）
        self.plot_xy_trajectory(ax1, data)

        # 2. 动作分量时序图（中）
        self.plot_action_components(ax2, data)

        # 3. 动作幅度图（右）
        self.plot_action_magnitude(ax3, data)
        
        # 调整布局
        plt.tight_layout()
        
        # 保存图片
        if output_path:
            plt.savefig(output_path, dpi=300, bbox_inches='tight')
            print(f"📊 可视化图片已保存到: {output_path}")
        
        # 显示图片
        if show_plot:
            plt.show()
        else:
            plt.close()
        
        return fig
    
    def plot_xy_trajectory(self, ax, data):
        """绘制XY轨迹图"""
        positions = data['positions']
        x_pos = positions[:, 0]
        y_pos = positions[:, 1]
        
        # 绘制轨迹线
        ax.plot(x_pos, y_pos, color=self.colors['trajectory'], linewidth=2, alpha=0.7)
        
        # 标记起点和终点
        ax.scatter(x_pos[0], y_pos[0], color=self.colors['start'], s=100, 
                  marker='o', label='Start', zorder=5, edgecolors='white', linewidth=2)
        ax.scatter(x_pos[-1], y_pos[-1], color=self.colors['end'], s=100, 
                  marker='X', label='End', zorder=5, edgecolors='white', linewidth=2)
        
        # 添加方向箭头
        if len(x_pos) > 1:
            for i in range(0, len(x_pos)-1, max(1, len(x_pos)//10)):
                dx = x_pos[i+1] - x_pos[i]
                dy = y_pos[i+1] - y_pos[i]
                if np.sqrt(dx**2 + dy**2) > 0:
                    ax.arrow(x_pos[i], y_pos[i], dx*0.3, dy*0.3, 
                            head_width=0.002, head_length=0.002, 
                            fc=self.colors['trajectory'], ec=self.colors['trajectory'], 
                            alpha=0.6)
        
        ax.set_xlabel('X Position', fontweight='bold')
        ax.set_ylabel('Y Position', fontweight='bold')
        ax.set_title('XY Trajectory', fontweight='bold')
        ax.legend()
        ax.grid(True, alpha=0.3)
        ax.set_aspect('equal', adjustable='box')
    
    def plot_action_components(self, ax, data):
        """绘制动作分量时序图"""
        positions = data['positions']
        time_steps = data['time_steps']
        
        # 绘制X、Y、Z分量
        ax.plot(time_steps, positions[:, 0], color=self.colors['x'], 
               linewidth=2, label='X', alpha=0.8)
        ax.plot(time_steps, positions[:, 1], color=self.colors['y'], 
               linewidth=2, label='Y', alpha=0.8)
        ax.plot(time_steps, positions[:, 2], color=self.colors['z'], 
               linewidth=2, label='Z', alpha=0.8)
        
        ax.set_xlabel('Time Step', fontweight='bold')
        ax.set_ylabel('Action Value', fontweight='bold')
        ax.set_title('Action Components Over Time', fontweight='bold')
        ax.legend()
        ax.grid(True, alpha=0.3)
    

    
    def plot_action_magnitude(self, ax, data):
        """绘制动作幅度图"""
        time_steps = data['time_steps']
        magnitude = data['action_magnitude']
        
        # 绘制幅度曲线
        ax.plot(time_steps, magnitude, color=self.colors['magnitude'], 
               linewidth=2, alpha=0.8)
        
        # 填充曲线下方区域
        ax.fill_between(time_steps, magnitude, alpha=0.3, color=self.colors['magnitude'])
        
        # 标记最大幅度点
        max_mag_idx = np.argmax(magnitude)
        ax.scatter(time_steps[max_mag_idx], magnitude[max_mag_idx], 
                  color=self.colors['end'], s=80, zorder=5, 
                  edgecolors='white', linewidth=2)
        
        ax.set_xlabel('Time Step', fontweight='bold')
        ax.set_ylabel('Action Magnitude', fontweight='bold')
        ax.set_title('Action Magnitude Over Time', fontweight='bold')
        ax.grid(True, alpha=0.3)
        ax.set_ylim(bottom=0)
    
    def visualize_episode(self, hdf5_path, output_dir=None, show_plot=False):
        """可视化单个episode"""
        hdf5_path = Path(hdf5_path)
        episode_id = hdf5_path.parent.parent.name
        
        print(f"🎨 生成episode {episode_id} 的可视化分析...")
        
        # 确定输出路径
        if output_dir:
            output_dir = Path(output_dir)
            output_dir.mkdir(parents=True, exist_ok=True)
            output_path = output_dir / f"{episode_id}_trajectory_analysis.png"
        else:
            output_path = hdf5_path.parent / f"{episode_id}_trajectory_analysis.png"
        
        # 创建可视化
        try:
            fig = self.create_visualization(hdf5_path, output_path, show_plot)
            print(f"✅ 可视化完成: {output_path}")
            return str(output_path)
            
        except Exception as e:
            print(f"❌ 可视化失败: {e}")
            raise

    def batch_visualize(self, data_dir, output_dir=None, pattern="**/trajectory.hdf5"):
        """批量可视化多个episode"""
        data_dir = Path(data_dir)

        # 查找所有HDF5文件
        hdf5_files = list(data_dir.glob(pattern))

        if not hdf5_files:
            print(f"❌ 在 {data_dir} 中未找到匹配 {pattern} 的HDF5文件")
            return []

        print(f"🔍 找到 {len(hdf5_files)} 个HDF5文件")

        # 批量处理
        output_paths = []
        for i, hdf5_path in enumerate(hdf5_files, 1):
            try:
                print(f"\n[{i}/{len(hdf5_files)}] 处理: {hdf5_path.name}")
                output_path = self.visualize_episode(
                    hdf5_path=hdf5_path,
                    output_dir=output_dir,
                    show_plot=False
                )
                output_paths.append(output_path)

            except Exception as e:
                print(f"❌ 处理 {hdf5_path.name} 失败: {e}")
                continue

        print(f"\n🎉 批量可视化完成! 成功处理 {len(output_paths)}/{len(hdf5_files)} 个文件")
        return output_paths

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='HDF5轨迹数据可视化分析')
    parser.add_argument('input_path', help='HDF5文件路径或包含HDF5文件的目录')
    parser.add_argument('--output-dir', '-o', help='输出目录路径')
    parser.add_argument('--show', action='store_true', help='显示图片')
    parser.add_argument('--batch', action='store_true', help='批量处理模式')
    parser.add_argument('--pattern', default='**/trajectory.hdf5', help='批量模式下的文件匹配模式')

    args = parser.parse_args()

    input_path = Path(args.input_path)

    # 检查输入路径是否存在
    if not input_path.exists():
        print(f"❌ 输入路径不存在: {input_path}")
        return 1

    # 创建可视化器
    visualizer = TrajectoryVisualizer()

    try:
        if args.batch or input_path.is_dir():
            # 批量处理模式
            print(f"🔄 批量处理模式: {input_path}")
            output_paths = visualizer.batch_visualize(
                data_dir=input_path,
                output_dir=args.output_dir,
                pattern=args.pattern
            )

            if output_paths:
                print(f"\n📁 生成的可视化文件:")
                for path in output_paths:
                    print(f"  - {path}")

        else:
            # 单文件处理模式
            print(f"📄 单文件处理模式: {input_path}")
            output_path = visualizer.visualize_episode(
                hdf5_path=input_path,
                output_dir=args.output_dir,
                show_plot=args.show
            )

            print(f"\n🎉 轨迹分析可视化完成!")
            print(f"📁 输出文件: {output_path}")

    except Exception as e:
        print(f"\n❌ 可视化失败: {e}")
        return 1

    return 0

if __name__ == "__main__":
    exit(main())
