#!/usr/bin/env python3
"""
文件1: 单个HDF5样本的完整数据提取器
为VLA模型训练提取单个episode的完整数据
"""

import h5py
import numpy as np
import json
import pickle
import cv2
import base64
from pathlib import Path
import argparse
from typing import Dict, Any, List, Optional

class VLADataExtractor:
    def __init__(self, output_format='json'):
        """
        初始化VLA数据提取器
        
        Args:
            output_format: 输出格式 ('json' 或 'pickle')
        """
        self.output_format = output_format
        
    def extract_trajectory_data(self, hdf5_file: h5py.File) -> Dict[str, Any]:
        """提取轨迹数据"""
        trajectory_data = {}
        
        # Master机器人关节位置
        if 'master/joint_position' in hdf5_file:
            master_joints = hdf5_file['master/joint_position'][:]
            trajectory_data['master_joint_positions'] = master_joints.tolist()
            trajectory_data['master_joint_stats'] = {
                'shape': master_joints.shape,
                'min': float(np.min(master_joints)),
                'max': float(np.max(master_joints)),
                'mean': float(np.mean(master_joints)),
                'std': float(np.std(master_joints))
            }
        
        # Puppet机器人关节位置
        if 'puppet/joint_position' in hdf5_file:
            puppet_joints = hdf5_file['puppet/joint_position'][:]
            trajectory_data['puppet_joint_positions'] = puppet_joints.tolist()
            trajectory_data['puppet_joint_stats'] = {
                'shape': puppet_joints.shape,
                'min': float(np.min(puppet_joints)),
                'max': float(np.max(puppet_joints)),
                'mean': float(np.mean(puppet_joints)),
                'std': float(np.std(puppet_joints))
            }
        
        # 末端执行器数据
        if 'puppet/end_effector' in hdf5_file:
            end_effector = hdf5_file['puppet/end_effector'][:]
            trajectory_data['end_effector_poses'] = end_effector.tolist()
            trajectory_data['end_effector_stats'] = {
                'shape': end_effector.shape,
                'min': float(np.min(end_effector)),
                'max': float(np.max(end_effector)),
                'mean': float(np.mean(end_effector)),
                'std': float(np.std(end_effector))
            }
        
        return trajectory_data
    
    def extract_language_data(self, hdf5_file: h5py.File) -> Dict[str, Any]:
        """提取语言数据"""
        language_data = {}
        
        # 原始语言指令
        if 'language_raw' in hdf5_file:
            raw_instruction = hdf5_file['language_raw'][:]
            if len(raw_instruction) > 0:
                instruction = raw_instruction[0]
                if isinstance(instruction, bytes):
                    instruction = instruction.decode('utf-8')
                language_data['instruction'] = instruction
        
        # 语言嵌入向量
        if 'language_distilbert' in hdf5_file:
            embedding = hdf5_file['language_distilbert'][:]
            language_data['embedding'] = embedding.tolist()
            language_data['embedding_stats'] = {
                'shape': embedding.shape,
                'min': float(np.min(embedding)),
                'max': float(np.max(embedding)),
                'mean': float(np.mean(embedding)),
                'std': float(np.std(embedding))
            }
        
        return language_data
    
    def extract_image_data(self, hdf5_file: h5py.File, save_images=True, 
                          output_dir: Optional[Path] = None) -> Dict[str, Any]:
        """提取图像数据"""
        image_data = {}
        cameras = ['camera_left', 'camera_right', 'camera_top']
        
        for camera in cameras:
            rgb_path = f'observations/rgb_images/{camera}'
            depth_path = f'observations/depth_images/{camera}'
            
            camera_data = {}
            
            # RGB图像
            if rgb_path in hdf5_file:
                rgb_dataset = hdf5_file[rgb_path]
                camera_data['rgb'] = {
                    'num_frames': len(rgb_dataset),
                    'frame_info': []
                }
                
                if save_images and output_dir:
                    # 保存图像文件
                    camera_dir = output_dir / f'images_{camera}'
                    camera_dir.mkdir(exist_ok=True)
                    
                    for i, frame_data in enumerate(rgb_dataset):
                        # 解码JPEG图像
                        frame_bytes = frame_data.tobytes()
                        img_array = np.frombuffer(frame_bytes, dtype=np.uint8)
                        img = cv2.imdecode(img_array, cv2.IMREAD_COLOR)
                        
                        if img is not None:
                            # 保存图像
                            img_path = camera_dir / f'frame_{i:04d}.png'
                            cv2.imwrite(str(img_path), img)
                            
                            camera_data['rgb']['frame_info'].append({
                                'frame_id': i,
                                'image_path': str(img_path),
                                'shape': img.shape,
                                'compressed_size': len(frame_data)
                            })
                else:
                    # 只保存元数据
                    for i, frame_data in enumerate(rgb_dataset):
                        frame_bytes = frame_data.tobytes()
                        img_array = np.frombuffer(frame_bytes, dtype=np.uint8)
                        img = cv2.imdecode(img_array, cv2.IMREAD_COLOR)
                        
                        if img is not None:
                            camera_data['rgb']['frame_info'].append({
                                'frame_id': i,
                                'shape': img.shape,
                                'compressed_size': len(frame_data)
                            })
            
            # 深度图像信息
            if depth_path in hdf5_file:
                depth_dataset = hdf5_file[depth_path]
                camera_data['depth'] = {
                    'num_frames': len(depth_dataset),
                    'frame_sizes': [len(frame) for frame in depth_dataset[:5]]  # 前5帧大小
                }
            
            image_data[camera] = camera_data
        
        return image_data
    
    def extract_metadata(self, hdf5_file: h5py.File, hdf5_path: Path) -> Dict[str, Any]:
        """提取元数据"""
        metadata = {
            'file_info': {
                'path': str(hdf5_path),
                'size_mb': hdf5_path.stat().st_size / (1024 * 1024),
                'episode_id': hdf5_path.parent.parent.name
            },
            'hdf5_attributes': dict(hdf5_file.attrs),
            'dataset_structure': {}
        }
        
        # 分析数据集结构
        def visit_func(name, obj):
            if isinstance(obj, h5py.Dataset):
                metadata['dataset_structure'][name] = {
                    'shape': obj.shape,
                    'dtype': str(obj.dtype),
                    'size_mb': obj.nbytes / (1024 * 1024)
                }
        
        hdf5_file.visititems(visit_func)
        
        # 计算轨迹长度
        if 'master/joint_position' in hdf5_file:
            metadata['trajectory_length'] = hdf5_file['master/joint_position'].shape[0]
        
        # 控制频率估算（假设10Hz）
        metadata['estimated_control_frequency'] = 10.0
        metadata['estimated_duration_seconds'] = metadata.get('trajectory_length', 0) / 10.0
        
        return metadata
    
    def get_preprocessing_params(self) -> Dict[str, Any]:
        """获取数据预处理参数"""
        return {
            'image_preprocessing': {
                'format': 'JPEG_compressed',
                'rgb_resolution': {
                    'camera_left': [480, 640, 3],
                    'camera_right': [480, 640, 3], 
                    'camera_top': [720, 1280, 3]
                },
                'normalization': 'uint8_0_255',
                'color_space': 'BGR'
            },
            'action_preprocessing': {
                'joint_positions': {
                    'dimensions': 8,
                    'units': 'radians',
                    'range': [-3.14, 3.14]
                },
                'end_effector': {
                    'dimensions': 6,
                    'format': 'position_orientation',
                    'position_units': 'meters',
                    'orientation_units': 'radians'
                }
            },
            'language_preprocessing': {
                'embedding_model': 'distilbert',
                'embedding_dim': 768,
                'max_sequence_length': 'variable'
            },
            'temporal_preprocessing': {
                'sampling_rate': '10Hz',
                'sequence_format': 'temporal_ordered'
            }
        }
    
    def extract_complete_episode(self, hdf5_path: str, output_path: str = None, 
                                save_images: bool = True) -> Dict[str, Any]:
        """
        提取单个episode的完整数据
        
        Args:
            hdf5_path: HDF5文件路径
            output_path: 输出文件路径
            save_images: 是否保存图像文件
            
        Returns:
            完整的episode数据字典
        """
        hdf5_path = Path(hdf5_path)
        
        if not hdf5_path.exists():
            raise FileNotFoundError(f"HDF5文件不存在: {hdf5_path}")
        
        # 创建输出目录
        if output_path:
            output_path = Path(output_path)
            output_dir = output_path.parent
            output_dir.mkdir(parents=True, exist_ok=True)
        else:
            output_dir = hdf5_path.parent / f"{hdf5_path.stem}_extracted"
            output_dir.mkdir(exist_ok=True)
            
            if self.output_format == 'json':
                output_path = output_dir / f"{hdf5_path.stem}_vla_data.json"
            else:
                output_path = output_dir / f"{hdf5_path.stem}_vla_data.pkl"
        
        print(f"🚀 提取episode数据: {hdf5_path.name}")
        print(f"📁 输出目录: {output_dir}")
        
        complete_data = {}
        
        try:
            with h5py.File(hdf5_path, 'r') as f:
                # 1. 提取轨迹数据
                print("  📊 提取轨迹数据...")
                complete_data['trajectory'] = self.extract_trajectory_data(f)
                
                # 2. 提取语言数据
                print("  💬 提取语言数据...")
                complete_data['language'] = self.extract_language_data(f)
                
                # 3. 提取图像数据
                print("  📷 提取图像数据...")
                complete_data['images'] = self.extract_image_data(
                    f, save_images=save_images, output_dir=output_dir if save_images else None
                )
                
                # 4. 提取元数据
                print("  📋 提取元数据...")
                complete_data['metadata'] = self.extract_metadata(f, hdf5_path)
                
                # 5. 添加预处理参数
                complete_data['preprocessing_params'] = self.get_preprocessing_params()
                
                # 6. 添加提取时间戳
                import datetime
                complete_data['extraction_info'] = {
                    'timestamp': datetime.datetime.now().isoformat(),
                    'extractor_version': '1.0',
                    'save_images': save_images,
                    'output_format': self.output_format
                }
        
        except Exception as e:
            print(f"❌ 提取失败: {e}")
            raise
        
        # 保存数据
        print(f"💾 保存数据到: {output_path}")

        if self.output_format == 'json':
            # 转换numpy类型为Python原生类型
            def convert_numpy(obj):
                if isinstance(obj, np.ndarray):
                    return obj.tolist()
                elif isinstance(obj, (np.integer, np.int64, np.int32)):
                    return int(obj)
                elif isinstance(obj, (np.floating, np.float64, np.float32)):
                    return float(obj)
                elif isinstance(obj, (np.bool_, bool)):
                    return bool(obj)
                elif isinstance(obj, dict):
                    return {k: convert_numpy(v) for k, v in obj.items()}
                elif isinstance(obj, list):
                    return [convert_numpy(item) for item in obj]
                elif isinstance(obj, tuple):
                    return tuple(convert_numpy(item) for item in obj)
                return obj

            serializable_data = convert_numpy(complete_data)

            with open(output_path, 'w', encoding='utf-8') as f:
                json.dump(serializable_data, f, indent=2, ensure_ascii=False)
        else:
            with open(output_path, 'wb') as f:
                pickle.dump(complete_data, f)
        
        print(f"✅ 数据提取完成!")
        print(f"  轨迹长度: {complete_data['metadata'].get('trajectory_length', 'N/A')} 步")
        print(f"  语言指令: {complete_data['language'].get('instruction', 'N/A')}")
        print(f"  图像帧数: {sum(cam.get('rgb', {}).get('num_frames', 0) for cam in complete_data['images'].values())}")
        
        return complete_data

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='提取单个HDF5文件的VLA训练数据')
    parser.add_argument('hdf5_path', help='HDF5文件路径')
    parser.add_argument('--output', '-o', help='输出文件路径')
    parser.add_argument('--format', choices=['json', 'pickle'], default='json', 
                       help='输出格式 (默认: json)')
    parser.add_argument('--no-images', action='store_true', 
                       help='不保存图像文件，只保存元数据')
    
    args = parser.parse_args()
    
    # 创建提取器
    extractor = VLADataExtractor(output_format=args.format)
    
    # 提取数据
    try:
        data = extractor.extract_complete_episode(
            hdf5_path=args.hdf5_path,
            output_path=args.output,
            save_images=not args.no_images
        )
        print(f"\n🎉 提取成功! 数据已保存。")
        
    except Exception as e:
        print(f"\n❌ 提取失败: {e}")
        return 1
    
    return 0

if __name__ == "__main__":
    exit(main())
