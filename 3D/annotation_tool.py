#!/usr/bin/env python3
"""
Semi-automatic Annotation Tool for 3D Detection Competition
Helps with depth extraction and annotation assistance
"""

import cv2
import numpy as np
import json
from pathlib import Path
import matplotlib.pyplot as plt
from matplotlib.patches import Rectangle
import argparse

class AnnotationHelper:
    """
    Helper tool for annotating RGB-D images
    """
    
    def __init__(self, rgb_path: str, depth_path: str):
        self.rgb_image = cv2.imread(rgb_path)
        self.rgb_image = cv2.cvtColor(self.rgb_image, cv2.COLOR_BGR2RGB)
        self.depth_image = cv2.imread(depth_path, cv2.IMREAD_ANYDEPTH)
        
        self.height, self.width = self.rgb_image.shape[:2]
        self.annotations = []
        
    def extract_depth_stats(self, bbox: list) -> dict:
        """
        Extract depth statistics from bounding box region
        
        Args:
            bbox: [x, y, w, h] in normalized coordinates
            
        Returns:
            Dictionary with depth statistics
        """
        # Convert normalized to pixel coordinates
        x = int(bbox[0] * self.width)
        y = int(bbox[1] * self.height)
        w = int(bbox[2] * self.width)
        h = int(bbox[3] * self.height)
        
        # Extract depth region
        depth_roi = self.depth_image[y:y+h, x:x+w]
        
        # Filter out invalid values
        valid_mask = (depth_roi > 0) & (depth_roi < 10000)
        valid_depths = depth_roi[valid_mask]
        
        if len(valid_depths) == 0:
            return {
                "mean": 0,
                "std": 0,
                "min": 0,
                "max": 0
            }
        
        # Convert to meters (assuming depth in mm)
        valid_depths = valid_depths / 1000.0
        
        return {
            "mean": float(np.mean(valid_depths)),
            "std": float(np.std(valid_depths)),
            "min": float(np.min(valid_depths)),
            "max": float(np.max(valid_depths))
        }
    
    def check_3d_object(self, bbox: list, threshold: float = 0.02) -> bool:
        """
        Check if object is 3D based on depth variation
        
        Args:
            bbox: [x, y, w, h] in normalized coordinates
            threshold: Minimum depth std for 3D object
            
        Returns:
            True if 3D object, False if 2D
        """
        depth_stats = self.extract_depth_stats(bbox)
        
        # Check depth variation
        if depth_stats["std"] < threshold:
            return False  # Likely 2D (flat)
        
        # Check depth gradient
        x = int(bbox[0] * self.width)
        y = int(bbox[1] * self.height)
        w = int(bbox[2] * self.width)
        h = int(bbox[3] * self.height)
        
        depth_roi = self.depth_image[y:y+h, x:x+w]
        
        # Calculate gradient magnitude
        grad_x = cv2.Sobel(depth_roi, cv2.CV_64F, 1, 0, ksize=3)
        grad_y = cv2.Sobel(depth_roi, cv2.CV_64F, 0, 1, ksize=3)
        grad_magnitude = np.sqrt(grad_x**2 + grad_y**2)
        
        # 3D objects should have significant depth gradients
        mean_gradient = np.mean(grad_magnitude[grad_magnitude > 0])
        
        return mean_gradient > 10  # Threshold for gradient
    
    def visualize_annotation(self, annotation: dict):
        """
        Visualize single annotation with depth info
        """
        fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(12, 6))
        
        # RGB image with bbox
        ax1.imshow(self.rgb_image)
        
        for obj in annotation.get('objects', []):
            bbox = obj['bbox']
            x = bbox[0] * self.width
            y = bbox[1] * self.height
            w = bbox[2] * self.width
            h = bbox[3] * self.height
            
            # Draw rectangle
            rect = Rectangle((x-w/2, y-h/2), w, h, 
                           linewidth=2, edgecolor='r', facecolor='none')
            ax1.add_patch(rect)
            
            # Add label
            label = f"{obj['class_name']} ({'3D' if obj['is_3d'] else '2D'})"
            ax1.text(x-w/2, y-h/2-5, label, 
                    bbox=dict(boxstyle="round,pad=0.3", facecolor="yellow", alpha=0.7))
        
        ax1.set_title('RGB Image with Annotations')
        ax1.axis('off')
        
        # Depth visualization
        depth_colored = cv2.applyColorMap(
            cv2.normalize(self.depth_image, None, 0, 255, cv2.NORM_MINMAX, cv2.CV_8U),
            cv2.COLORMAP_JET
        )
        ax2.imshow(depth_colored)
        ax2.set_title('Depth Map')
        ax2.axis('off')
        
        plt.tight_layout()
        plt.show()
    
    def create_annotation_template(self, image_id: str) -> dict:
        """
        Create annotation template for current image
        """
        return {
            "image_id": image_id,
            "sensor_data": {
                "rgb_path": f"rgb/{image_id}.jpg",
                "depth_path": f"depth/{image_id}.png",
                "camera_intrinsics": {
                    "fx": 572.41,
                    "fy": 573.57, 
                    "cx": 325.26,
                    "cy": 242.04
                }
            },
            "objects": [],
            "scene_info": {
                "background_type": None,
                "lighting": None,
                "platform": "static"
            }
        }
    
    def add_object_annotation(self, annotation: dict, bbox: list, 
                            class_id: str, class_name: str) -> dict:
        """
        Add object annotation with automatic depth analysis
        """
        depth_stats = self.extract_depth_stats(bbox)
        is_3d = self.check_3d_object(bbox)
        
        obj_anno = {
            "id": len(annotation['objects']),
            "bbox": bbox,
            "class_id": class_id,
            "class_name": class_name,
            "is_3d": is_3d,
            "depth_stats": depth_stats,
            "occlusion": 0.0,
            "truncation": 0.0,
            "confidence": 1.0
        }
        
        annotation['objects'].append(obj_anno)
        return annotation

# Example usage script
def main():
    parser = argparse.ArgumentParser(description='Annotation Helper Tool')
    parser.add_argument('--rgb', required=True, help='Path to RGB image')
    parser.add_argument('--depth', required=True, help='Path to depth image')
    parser.add_argument('--output', default='annotation.json', help='Output annotation file')
    
    args = parser.parse_args()
    
    # Initialize helper
    helper = AnnotationHelper(args.rgb, args.depth)
    
    # Create annotation
    image_id = Path(args.rgb).stem
    annotation = helper.create_annotation_template(image_id)
    
    # Example: Add detected objects (in practice, these would come from YOLO or manual input)
    # Format: [x_center, y_center, width, height] in normalized coordinates
    
    # Green can (approximate from image)
    helper.add_object_annotation(
        annotation, 
        bbox=[0.45, 0.35, 0.08, 0.15],
        class_id="CC001",
        class_name="canned_drink"
    )
    
    # Orange on table
    helper.add_object_annotation(
        annotation,
        bbox=[0.55, 0.40, 0.10, 0.10],
        class_id="CD002", 
        class_name="orange"
    )
    
    # Visualize
    helper.visualize_annotation(annotation)
    
    # Save annotation
    with open(args.output, 'w') as f:
        json.dump(annotation, f, indent=2)
    
    print(f"Annotation saved to {args.output}")

if __name__ == '__main__':
    main() 