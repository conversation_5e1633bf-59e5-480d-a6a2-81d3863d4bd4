# 3D Detection Competition - 数据集标注指南

## 基于示例图片的标注流程

### 1. 物体检测与标注

#### 已识别物体：
1. **绿色罐装饮料** (桌面上)
   - 类别：`CC001` (罐装饮料)
   - 位置：桌面中央偏右
   - 深度信息：需要记录完整的深度变化

2. **橙子#1** (桌面上)
   - 类别：`CD002` (橙子)
   - 位置：桌面右侧
   - 特征：球形，有自然纹理

3. **橙子#2** (地板上)
   - 类别：`CD002` (橙子)
   - 位置：桌子下方地板
   - **重要**：这是场外干扰物，但仍需标注

### 2. 关键标注要素

#### 2.1 边界框 (Bounding Box)
```python
"bbox": [x_center, y_center, width, height]  # 归一化坐标
```
- 使用归一化坐标 (0-1)
- 紧贴物体边缘
- 包含物体的所有可见部分

#### 2.2 3D/2D 判别
```python
"is_3d": true  # 所有真实物体
"is_3d": false # 仅用于打印的图片
```
**判别依据**：
- 检查深度图中的连续变化
- 3D物体：有明显的深度梯度
- 2D图片：深度值平坦一致

#### 2.3 深度统计
```python
"depth_stats": {
  "mean": 1.15,   # 平均深度
  "std": 0.02,    # 深度标准差
  "min": 1.10,    # 最小深度
  "max": 1.20     # 最大深度
}
```
**计算方法**：
1. 提取bbox内的深度值
2. 过滤无效值(0或inf)
3. 计算统计量

### 3. 场景信息标注

#### 3.1 背景分析
- **类型**：`mixed_color` (混合色彩)
- **细节**：记录所有颜色和图案
- **潜在干扰**：彩色背景可能影响检测

#### 3.2 光照条件
- **光源类型**：`artificial_lamp` (人工台灯)
- **位置**：左侧
- **影响**：注意阴影区域

### 4. 特殊情况处理

#### 4.1 场外物体
- **必须标注**：地板上的橙子
- **添加属性**：`"location": "floor"`
- **原因**：训练模型区分场内外物体

#### 4.2 遮挡处理
```python
"occlusion": 0.0-1.0  # 遮挡程度
```
- 0.0：完全可见
- 0.5：50%被遮挡
- 1.0：完全被遮挡（不标注）

### 5. 标注工具推荐

#### 5.1 使用LabelMe或CVAT
1. 加载RGB图像
2. 同时打开对应深度图
3. 标注边界框
4. 填写属性

#### 5.2 半自动标注流程
```python
# 1. 运行预训练模型获取初始检测
initial_detections = yolo_model(rgb_image)

# 2. 人工校正边界框
manual_correction(initial_detections)

# 3. 自动提取深度信息
depth_stats = extract_depth_stats(depth_image, bbox)

# 4. 人工验证3D/2D属性
verify_3d_status(rgb_crop, depth_crop)
```

### 6. 质量控制要点

#### 6.1 必须检查项
- [ ] 所有可见物体都已标注
- [ ] 边界框紧贴物体
- [ ] 3D/2D属性正确
- [ ] 深度统计合理
- [ ] 场景信息完整

#### 6.2 常见错误
1. **漏标场外物体**：地板上的物体也要标
2. **错误的3D/2D判断**：必须查看深度图
3. **边界框过大**：应紧贴物体边缘
4. **忽略部分遮挡**：即使部分可见也要标注

### 7. 数据集组织

#### 7.1 文件命名规范
```
场景类型_序号_时间戳
例：static_scene_001_20240110143000
```

#### 7.2 目录结构
```
scene_name/
├── rgb/
│   └── scene_001.jpg
├── depth/
│   └── scene_001.png
├── annotations/
│   └── scene_001.json
└── metadata.json
```

### 8. 批量标注建议

#### 8.1 分阶段标注
1. **第一轮**：标注所有物体边界框
2. **第二轮**：添加3D/2D属性
3. **第三轮**：提取深度信息
4. **第四轮**：场景信息和质检

#### 8.2 团队协作
- 使用版本控制(Git)管理标注文件
- 定期交叉验证
- 维护标注一致性文档

### 9. 针对比赛的特殊考虑

#### 9.1 2D干扰物
- 仔细检查是否有打印的图片
- 这些是关键的负样本

#### 9.2 动态场景准备
- 标注时考虑物体可能的运动
- 记录清晰度和运动模糊

#### 9.3 未知物体
- 预留`W***`编号空间
- 详细记录物体特征

这份标注指南将确保数据集的高质量和一致性，为训练强大的3D检测系统奠定基础。 