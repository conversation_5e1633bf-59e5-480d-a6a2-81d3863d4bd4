#!/usr/bin/env python3
"""
Enhanced Dataset Construction Plan
For 3D Detection Competition

Dataset Structure:
dataset_3d/
├── known_objects/          # 16 known classes
│   ├── rgb/
│   ├── depth/
│   ├── annotations/
│   └── metadata.json
├── unknown_objects/        # Open-set training
│   ├── rgb/
│   ├── depth/
│   └── annotations/
├── mixed_scenes/          # Complex scenarios
│   ├── static/
│   ├── dynamic/
│   └── multi_view/
├── 2d_3d_discrimination/  # 2D vs 3D training
│   ├── real_objects/
│   ├── printed_images/
│   └── labels/
└── augmented/             # Synthetic data
"""

import json
import os
import cv2
import numpy as np
from pathlib import Path
from typing import Dict, List, Tuple

class EnhancedDatasetBuilder:
    """
    Builds multi-modal dataset for 3D detection competition
    """
    
    def __init__(self, base_path: str = 'dataset_3d'):
        self.base_path = Path(base_path)
        self.annotation_format = {
            'version': '1.0',
            'type': 'multi_modal_3d_detection'
        }
        
    def create_directory_structure(self):
        """Create comprehensive dataset directory structure"""
        directories = [
            'known_objects/rgb',
            'known_objects/depth',
            'known_objects/annotations',
            'unknown_objects/rgb',
            'unknown_objects/depth',
            'unknown_objects/annotations',
            'mixed_scenes/static',
            'mixed_scenes/dynamic',
            'mixed_scenes/multi_view',
            '2d_3d_discrimination/real_objects',
            '2d_3d_discrimination/printed_images',
            '2d_3d_discrimination/labels',
            'augmented/synthetic_scenes',
            'augmented/domain_randomization'
        ]
        
        for dir_path in directories:
            (self.base_path / dir_path).mkdir(parents=True, exist_ok=True)
    
    def create_enhanced_annotation(self, image_id: str) -> Dict:
        """
        Create enhanced annotation format beyond simple YOLO
        
        Includes:
        - Bounding boxes with depth info
        - 3D/2D classification
        - Occlusion levels
        - Temporal information
        - Scene metadata
        """
        annotation = {
            'image_id': image_id,
            'timestamp': None,
            'sensor_data': {
                'rgb_path': f'rgb/{image_id}.jpg',
                'depth_path': f'depth/{image_id}.png',
                'camera_intrinsics': None,
                'camera_pose': None
            },
            'objects': [],
            'scene_info': {
                'background_type': None,  # 'white', 'mixed', 'textured'
                'lighting': None,         # 'natural', 'yellow', 'white'
                'platform': None,         # 'static', 'rotating'
                'rotation_speed': None    # if rotating
            }
        }
        
        return annotation
    
    def add_object_annotation(self, annotation: Dict, obj_info: Dict):
        """
        Add detailed object annotation
        
        obj_info should contain:
        - bbox: [x, y, w, h] normalized
        - class_id: Known class ID or 'unknown'
        - is_3d: Boolean (True for real object, False for printed image)
        - depth_stats: {'mean': float, 'std': float, 'min': float, 'max': float}
        - occlusion: float (0.0 to 1.0)
        - truncation: float (0.0 to 1.0)
        - confidence: float (annotation confidence)
        """
        object_anno = {
            'id': len(annotation['objects']),
            'bbox': obj_info['bbox'],
            'class_id': obj_info['class_id'],
            'class_name': self._get_class_name(obj_info['class_id']),
            'is_3d': obj_info['is_3d'],
            'depth_stats': obj_info.get('depth_stats', {}),
            'occlusion': obj_info.get('occlusion', 0.0),
            'truncation': obj_info.get('truncation', 0.0),
            'confidence': obj_info.get('confidence', 1.0),
            'attributes': obj_info.get('attributes', {})
        }
        
        annotation['objects'].append(object_anno)
    
    def generate_data_collection_plan(self) -> Dict:
        """
        Comprehensive data collection plan
        """
        plan = {
            'phase1_known_objects': {
                'description': 'Collect individual known objects',
                'requirements': {
                    'objects': ['All 16 known classes'],
                    'views_per_object': 50,
                    'backgrounds': ['white', 'textured', 'cluttered'],
                    'lighting': ['natural', 'yellow_lamp', 'white_lamp'],
                    'distances': [1.0, 1.4, 1.8],  # meters
                    'angles': [0, 15, 30, 45],      # degrees
                }
            },
            'phase2_2d_3d_discrimination': {
                'description': 'Collect real objects vs printed images',
                'requirements': {
                    'real_objects': 200,
                    'printed_images': 200,
                    'mixed_scenes': 100,
                    'focus': 'Depth pattern differences'
                }
            },
            'phase3_unknown_objects': {
                'description': 'Collect diverse unknown objects',
                'requirements': {
                    'object_categories': ['household', 'toys', 'tools', 'random'],
                    'total_objects': 50,
                    'views_per_object': 20
                }
            },
            'phase4_complex_scenes': {
                'description': 'Competition-like scenarios',
                'requirements': {
                    'static_scenes': 100,
                    'rotating_scenes': 50,
                    'multi_table_scenes': 50,
                    'objects_per_scene': '7-15',
                    'with_occlusions': True,
                    'with_2d_distractors': True
                }
            },
            'phase5_augmentation': {
                'description': 'Synthetic data generation',
                'techniques': [
                    'Domain randomization',
                    'Copy-paste augmentation',
                    'Depth synthesis',
                    'Lighting variations',
                    'Motion blur for rotating scenes'
                ]
            }
        }
        
        return plan
    
    def create_training_splits(self):
        """
        Create specialized training splits for each model
        """
        splits = {
            'primary_yolo_split': {
                'train': 'known_objects/train_yolo.txt',
                'val': 'known_objects/val_yolo.txt',
                'description': 'Standard YOLO format for 16 classes'
            },
            'depth_analyzer_split': {
                'train': '2d_3d_discrimination/train_pairs.json',
                'val': '2d_3d_discrimination/val_pairs.json',
                'description': 'Paired RGB-D crops with 2D/3D labels'
            },
            'openset_split': {
                'train': 'mixed_known_unknown_train.json',
                'val': 'mixed_known_unknown_val.json',
                'description': 'Mixed known/unknown for anomaly detection'
            },
            'temporal_split': {
                'train': 'dynamic_sequences_train.json',
                'val': 'dynamic_sequences_val.json',
                'description': 'Video sequences from rotating platform'
            }
        }
        
        return splits

# Data augmentation strategies
class CompetitionAugmentation:
    """
    Specialized augmentation for competition scenarios
    """
    
    @staticmethod
    def add_2d_distractors(image: np.ndarray, depth: np.ndarray) -> Tuple:
        """
        Add printed image distractors to scene
        - Random placement on surfaces
        - Realistic depth values (flat)
        """
        # Implementation details...
        pass
    
    @staticmethod
    def simulate_rotation(image_sequence: List[np.ndarray]) -> List[np.ndarray]:
        """
        Simulate rotating platform effects
        - Motion blur
        - Consistent object tracking
        """
        # Implementation details...
        pass
    
    @staticmethod
    def vary_lighting(image: np.ndarray, light_type: str) -> np.ndarray:
        """
        Simulate different lighting conditions
        - Yellow lamp
        - White lamp
        - Mixed lighting
        """
        # Implementation details...
        pass

# Metadata structure for competition
COMPETITION_METADATA = {
    'known_classes': {
        'CA001': 'spoon',
        'CA002': 'chopsticks',
        'CA003': 'bowl',
        'CA004': 'hanger',
        'CB001': 'shaqima',
        'CB002': 'preserved_fruit',
        'CB003': 'sausage',
        'CB004': 'chips',
        'CC001': 'canned_drink',
        'CC002': 'bottled_drink',
        'CC003': 'milk_box',
        'CC004': 'water_bottle',
        'CD001': 'apple',
        'CD002': 'orange',
        'CD003': 'banana',
        'CD004': 'mango'
    },
    'scene_configurations': {
        'round1': {
            'tables': 1,
            'table_type': 'rotating',
            'objects': '7-15',
            'time_limit': 50
        },
        'round2': {
            'tables': 3,
            'table_types': ['square', 'square', 'rotating'],
            'objects_per_table': '7-15',
            'time_limit': 150
        }
    }
} 