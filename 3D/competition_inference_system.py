#!/usr/bin/env python3
"""
Competition Inference System
For 3D Detection Competition

Features:
- Multi-model cascade inference
- Real-time performance optimization
- Judge box communication
- Automatic camera control
"""

import socket
import json
import time
import threading
import numpy as np
import cv2
import torch
from pathlib import Path
from typing import Dict, List, Tuple, Optional
from queue import Queue
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class CompetitionInferenceSystem:
    """
    Main inference system for competition
    """
    
    def __init__(self, model_paths: Dict[str, str]):
        # Load all models
        self.models = self._load_models(model_paths)
        
        # Communication with judge box
        self.judge_communicator = JudgeBoxCommunicator()
        
        # Camera controller
        self.camera_controller = CameraController()
        
        # Results buffer
        self.results_buffer = []
        
        # Performance monitoring
        self.timing_stats = {}
        
        # Competition parameters
        self.round1_time_limit = 50  # seconds
        self.round2_time_limit = 150  # seconds
        
    def _load_models(self, model_paths: Dict[str, str]) -> Dict:
        """Load all trained models"""
        models = {}
        
        # Primary YOLO
        from ultralytics import YOLO
        models['yolo'] = YOLO(model_paths['yolo'])
        models['yolo'].to('cuda')
        
        # Depth Analyzer
        models['depth_analyzer'] = torch.load(model_paths['depth_analyzer'])
        models['depth_analyzer'].eval()
        
        # Open-set Detector
        models['openset'] = torch.load(model_paths['openset'])
        models['openset'].eval()
        
        # Temporal Fusion
        models['temporal'] = torch.load(model_paths['temporal'])
        
        logger.info("All models loaded successfully")
        return models
    
    def run_round1(self):
        """
        Round 1: Single rotating table
        """
        logger.info("Starting Round 1 Inference")
        
        # Wait for start signal
        self.judge_communicator.wait_for_start()
        
        start_time = time.time()
        frame_buffer = []
        results = []
        
        while time.time() - start_time < self.round1_time_limit:
            # Capture frame
            rgb, depth = self.camera_controller.capture_rgbd()
            timestamp = time.time() - start_time
            
            # Process frame
            frame_results = self._process_single_frame(rgb, depth, timestamp)
            
            # Buffer for temporal fusion
            frame_buffer.append({
                'rgb': rgb,
                'depth': depth,
                'timestamp': timestamp,
                'detections': frame_results
            })
            
            # Temporal fusion every 10 frames
            if len(frame_buffer) >= 10:
                fused_results = self._temporal_fusion(frame_buffer)
                results.extend(fused_results)
                frame_buffer = frame_buffer[5:]  # Keep overlap
        
        # Final processing
        final_results = self._aggregate_results(results)
        
        # Send to judge box
        self.judge_communicator.send_results(final_results)
        
        # Save backup
        self._save_results_file(final_results, round_num=1)
        
        logger.info(f"Round 1 completed in {time.time() - start_time:.2f}s")
    
    def run_round2(self):
        """
        Round 2: Three tables with camera rotation
        """
        logger.info("Starting Round 2 Inference")
        
        # Wait for start signal
        self.judge_communicator.wait_for_start()
        
        start_time = time.time()
        all_results = []
        
        for table_idx in range(3):
            logger.info(f"Processing table {table_idx + 1}")
            
            # Rotate camera to table
            if table_idx > 0:
                self.camera_controller.rotate_to_table(table_idx)
                time.sleep(0.5)  # Wait for rotation
            
            # Process this table
            table_results = self._process_table_view(table_idx)
            all_results.extend(table_results)
            
            # Check time limit
            if time.time() - start_time > self.round2_time_limit - 10:
                logger.warning("Approaching time limit, finalizing results")
                break
        
        # Aggregate all results
        final_results = self._aggregate_results(all_results)
        
        # Send to judge box
        self.judge_communicator.send_results(final_results)
        
        # Save backup
        self._save_results_file(final_results, round_num=2)
        
        logger.info(f"Round 2 completed in {time.time() - start_time:.2f}s")
    
    def _process_single_frame(self, rgb: np.ndarray, depth: np.ndarray, 
                            timestamp: float) -> List[Dict]:
        """
        Process single frame through model cascade
        """
        t0 = time.time()
        
        # Stage 1: YOLO detection
        yolo_results = self.models['yolo'](rgb, conf=0.3, verbose=False)[0]
        
        detections = []
        
        if yolo_results.boxes is not None:
            for box in yolo_results.boxes:
                x1, y1, x2, y2 = box.xyxy[0].cpu().numpy()
                
                # Extract crop for depth analysis
                rgb_crop = rgb[int(y1):int(y2), int(x1):int(x2)]
                depth_crop = depth[int(y1):int(y2), int(x1):int(x2)]
                
                # Stage 2: 3D/2D classification
                is_3d = self._check_3d_object(rgb_crop, depth_crop)
                
                if is_3d:  # Only keep 3D objects
                    detection = {
                        'bbox': [x1, y1, x2, y2],
                        'class_id': int(box.cls.item()),
                        'class_name': self.models['yolo'].names[int(box.cls.item())],
                        'confidence': float(box.conf.item()),
                        'is_3d': True,
                        'timestamp': timestamp
                    }
                    detections.append(detection)
        
        # Stage 3: Unknown object detection
        unknown_detections = self._detect_unknown_objects(rgb, depth, detections)
        detections.extend(unknown_detections)
        
        self.timing_stats['frame_processing'] = time.time() - t0
        
        return detections
    
    def _check_3d_object(self, rgb_crop: np.ndarray, 
                        depth_crop: np.ndarray) -> bool:
        """
        Check if detected object is 3D using depth analyzer
        """
        # Preprocess
        rgb_tensor = torch.from_numpy(rgb_crop).float().permute(2, 0, 1)
        depth_tensor = torch.from_numpy(depth_crop).float().unsqueeze(0)
        
        # Resize to fixed size
        rgb_tensor = torch.nn.functional.interpolate(
            rgb_tensor.unsqueeze(0), size=(128, 128)
        )
        depth_tensor = torch.nn.functional.interpolate(
            depth_tensor.unsqueeze(0), size=(128, 128)
        )
        
        # Analyze
        with torch.no_grad():
            is_3d_prob = self.models['depth_analyzer'](rgb_tensor, depth_tensor)
        
        return is_3d_prob > 0.5
    
    def _detect_unknown_objects(self, rgb: np.ndarray, depth: np.ndarray,
                               known_detections: List[Dict]) -> List[Dict]:
        """
        Detect unknown objects not found by YOLO
        """
        # Create mask of known detections
        mask = np.ones(rgb.shape[:2], dtype=bool)
        for det in known_detections:
            x1, y1, x2, y2 = [int(v) for v in det['bbox']]
            mask[y1:y2, x1:x2] = False
        
        # Run open-set detector on unmasked regions
        with torch.no_grad():
            unknown_objects = self.models['openset'].detect(
                rgb, depth, mask
            )
        
        # Format results
        unknown_detections = []
        for idx, obj in enumerate(unknown_objects):
            unknown_detections.append({
                'bbox': obj['bbox'],
                'class_id': f'W{idx+1:03d}',
                'class_name': 'unknown',
                'confidence': obj['confidence'],
                'is_3d': True,
                'timestamp': time.time()
            })
        
        return unknown_detections
    
    def _temporal_fusion(self, frame_buffer: List[Dict]) -> List[Dict]:
        """
        Apply temporal fusion to frame buffer
        """
        # Extract sequences
        detections_sequence = [f['detections'] for f in frame_buffer]
        timestamps = [f['timestamp'] for f in frame_buffer]
        
        # Run temporal fusion model
        fused_detections = self.models['temporal'].fuse_sequence(
            detections_sequence, timestamps
        )
        
        return fused_detections
    
    def _aggregate_results(self, all_detections: List[Dict]) -> Dict:
        """
        Aggregate detections and count objects
        """
        # Group by class
        class_counts = {}
        
        for det in all_detections:
            class_id = det['class_id']
            if class_id not in class_counts:
                class_counts[class_id] = 0
            class_counts[class_id] += 1
        
        # Format for judge box
        results = {
            'objects': []
        }
        
        for class_id, count in class_counts.items():
            results['objects'].append({
                'id': class_id,
                'num': count
            })
        
        return results
    
    def _save_results_file(self, results: Dict, round_num: int):
        """
        Save results to file as backup
        """
        filename = f"~/Desktop/result_r{round_num}/SJTU-Team1-R{round_num}.txt"
        
        with open(filename, 'w') as f:
            f.write("START\n")
            for obj in results['objects']:
                f.write(f"{obj['id']};{obj['num']}\n")
            f.write("END\n")
        
        logger.info(f"Results saved to {filename}")

class JudgeBoxCommunicator:
    """
    Handles communication with judge box software
    """
    
    def __init__(self, host='localhost', port=8888):
        self.host = host
        self.port = port
        self.socket = None
        
    def connect(self):
        """Establish connection to judge box"""
        self.socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        self.socket.connect((self.host, self.port))
        logger.info(f"Connected to judge box at {self.host}:{self.port}")
    
    def wait_for_start(self):
        """Wait for start signal from judge"""
        logger.info("Waiting for start signal...")
        data = self.socket.recv(1024).decode()
        if data == "START":
            logger.info("Start signal received!")
            return True
        return False
    
    def send_results(self, results: Dict):
        """Send results to judge box"""
        message = json.dumps(results)
        self.socket.send(message.encode())
        logger.info("Results sent to judge box")

class CameraController:
    """
    Controls camera and handles RGB-D capture
    """
    
    def __init__(self):
        # Initialize Orbbec Astra+ camera
        import pyrealsense2 as rs  # Or appropriate SDK
        self.pipeline = self._init_camera()
        
    def capture_rgbd(self) -> Tuple[np.ndarray, np.ndarray]:
        """Capture synchronized RGB-D frame"""
        # Implementation depends on camera SDK
        # Return RGB and depth images
        pass
    
    def rotate_to_table(self, table_idx: int):
        """Send rotation command to judge box"""
        # Implementation for camera rotation
        pass

# Main competition entry point
def main():
    """Main competition execution"""
    
    # Model paths
    model_paths = {
        'yolo': 'models/competition/yolo_best.pt',
        'depth_analyzer': 'models/competition/depth_analyzer.pth',
        'openset': 'models/competition/openset_detector.pth',
        'temporal': 'models/competition/temporal_fusion.pth'
    }
    
    # Initialize system
    system = CompetitionInferenceSystem(model_paths)
    
    # Connect to judge box
    system.judge_communicator.connect()
    
    # Determine round from command line or judge box
    round_num = 1  # or get from args
    
    if round_num == 1:
        system.run_round1()
    else:
        system.run_round2()
    
    logger.info("Competition inference completed!")

if __name__ == '__main__':
    main() 