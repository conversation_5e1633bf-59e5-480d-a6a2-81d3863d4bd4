#!/usr/bin/env python3
"""
SimplerEnv增强接口 - 友好易用的多物体场景生成

提供分层接口设计：
- 简单接口：一键生成，使用预设配置
- 中级接口：自定义配置，适合有经验用户
- 高级接口：完全自定义，适合专家用户

主要改进：
- 预设配置系统，开箱即用
- 智能物体放置，解决掉落问题
- 完善的错误处理和用户反馈
- 配置验证和兼容性检查
"""

import os
import sys
import numpy as np
import cv2
from pathlib import Path
from typing import Dict, List, Tuple, Optional, Union
from dataclasses import dataclass
from enum import Enum
import random
import json

# 添加路径
sys.path.append(str(Path(__file__).parent))
sys.path.append(str(Path(__file__).parent / "ManiSkill2_real2sim"))

# 设置环境变量
os.environ["MS2_REAL2SIM_ASSET_DIR"] = str(Path(__file__).parent / "ManiSkill2_real2sim" / "data")


# ==================== 配置管理系统 ====================

class PresetType(Enum):
    """预设配置类型"""
    DESKTOP_PICKING = "desktop_picking"
    MULTI_OBJECT_SORTING = "multi_object_sorting"
    BRIDGE_DATASET = "bridge_dataset"
    INDUSTRIAL_SCENE = "industrial_scene"
    LABORATORY_SCENE = "laboratory_scene"


@dataclass
class SceneConfig:
    """场景配置数据类"""
    name: str
    description: str
    robot_type: str
    camera_type: str
    lighting_mode: str
    workspace_bounds: Tuple[Tuple[float, float], Tuple[float, float]]
    max_objects: int
    recommended_objects: List[str]
    control_frequency: int = 3
    simulation_frequency: int = 513


# 预设配置库
PRESET_CONFIGS = {
    PresetType.DESKTOP_PICKING: SceneConfig(
        name="桌面物体抓取",
        description="适合单物体或少量物体的桌面抓取任务，高成功率",
        robot_type="google_robot_static",
        camera_type="overhead_camera",
        lighting_mode="indoor_bright",
        workspace_bounds=((-0.28, -0.22), (0.12, 0.18)),
        max_objects=8,
        recommended_objects=["apple", "orange", "coke_can", "pepsi_can", "sprite_can", "green_cube_3cm", "yellow_cube_3cm", "sponge"],
        control_frequency=3
    ),

    PresetType.MULTI_OBJECT_SORTING: SceneConfig(
        name="多物体分拣",
        description="适合5-8个物体的复杂分拣场景，中等难度",
        robot_type="google_robot_static",
        camera_type="overhead_camera",
        lighting_mode="laboratory",
        workspace_bounds=((-0.30, -0.20), (0.10, 0.20)),
        max_objects=12,
        recommended_objects=["apple", "orange", "coke_can", "pepsi_can", "sprite_can", "green_cube_3cm", "yellow_cube_3cm", "sponge", "blue_plastic_bottle", "opened_coke_can", "opened_pepsi_can", "fanta_can"],
        control_frequency=3
    ),

    PresetType.BRIDGE_DATASET: SceneConfig(
        name="Bridge数据集风格",
        description="模拟Bridge数据集的真实机器人环境",
        robot_type="widowx",
        camera_type="base_camera",
        lighting_mode="natural",
        workspace_bounds=((-0.35, 0.05), (-0.15, 0.15)),
        max_objects=10,
        recommended_objects=["sponge", "blue_plastic_bottle", "apple", "orange", "coke_can", "pepsi_can", "green_cube_3cm", "yellow_cube_3cm", "bridge_carrot_generated_modified", "eggplant"],
        control_frequency=5
    ),

    PresetType.INDUSTRIAL_SCENE: SceneConfig(
        name="工业场景",
        description="模拟工业环境的物体操作",
        robot_type="google_robot_static",
        camera_type="overhead_camera",
        lighting_mode="industrial",
        workspace_bounds=((-0.32, -0.18), (0.08, 0.22)),
        max_objects=10,
        recommended_objects=["coke_can", "pepsi_can", "sprite_can", "blue_plastic_bottle", "7up_can", "fanta_can", "redbull_can", "green_cube_3cm", "yellow_cube_3cm", "apple"],
        control_frequency=3
    ),

    PresetType.LABORATORY_SCENE: SceneConfig(
        name="实验室场景",
        description="模拟实验室环境的精密操作",
        robot_type="google_robot_static",
        camera_type="overhead_camera",
        lighting_mode="laboratory_precise",
        workspace_bounds=((-0.26, -0.24), (0.14, 0.16)),
        max_objects=6,
        recommended_objects=["apple", "orange", "green_cube_3cm", "yellow_cube_3cm", "coke_can", "pepsi_can"],
        control_frequency=3
    )
}


# 物体库管理
# 物体库管理 - 包含完整的物理参数
AVAILABLE_OBJECTS = {
    # 饮料类 - 未开封
    "pepsi_can": {
        "category": "can", 
        "size": "medium", 
        "difficulty": "medium",
        "bbox": {"min": [-0.033, -0.0615, -0.033], "max": [0.033, 0.0615, 0.033]},
        "density": 1000
    },
    "coke_can": {
        "category": "can", 
        "size": "medium", 
        "difficulty": "medium",
        "bbox": {"min": [-0.033, -0.0615, -0.033], "max": [0.033, 0.0615, 0.033]},
        "density": 1000
    },
    "sprite_can": {
        "category": "can", 
        "size": "medium", 
        "difficulty": "medium",
        "bbox": {"min": [-0.033, -0.0615, -0.033], "max": [0.033, 0.0615, 0.033]},
        "density": 1000
    },
    "7up_can": {
        "category": "can", 
        "size": "medium", 
        "difficulty": "medium",
        "bbox": {"min": [-0.033, -0.0615, -0.033], "max": [0.033, 0.0615, 0.033]},
        "density": 1000
    },
    "fanta_can": {
        "category": "can", 
        "size": "medium", 
        "difficulty": "medium",
        "bbox": {"min": [-0.033, -0.0615, -0.033], "max": [0.033, 0.0615, 0.033]},
        "density": 1000
    },
    "redbull_can": {
        "category": "can", 
        "size": "small", 
        "difficulty": "medium",
        "bbox": {"min": [-0.02925, -0.066, -0.02925], "max": [0.02925, 0.066, 0.02925]},
        "density": 1000
    },
    
    # 饮料类 - 已开封
    "opened_pepsi_can": {
        "category": "can_opened", 
        "size": "medium", 
        "difficulty": "hard",
        "bbox": {"min": [-0.033, -0.0615, -0.033], "max": [0.033, 0.0615, 0.033]},
        "density": 50
    },
    "opened_coke_can": {
        "category": "can_opened", 
        "size": "medium", 
        "difficulty": "hard",
        "bbox": {"min": [-0.033, -0.0615, -0.033], "max": [0.033, 0.0615, 0.033]},
        "density": 50
    },
    "opened_sprite_can": {
        "category": "can_opened", 
        "size": "medium", 
        "difficulty": "hard",
        "bbox": {"min": [-0.033, -0.0615, -0.033], "max": [0.033, 0.0615, 0.033]},
        "density": 50
    },
    "opened_7up_can": {
        "category": "can_opened", 
        "size": "medium", 
        "difficulty": "hard",
        "bbox": {"min": [-0.033, -0.0615, -0.033], "max": [0.033, 0.0615, 0.033]},
        "density": 50
    },
    "opened_fanta_can": {
        "category": "can_opened", 
        "size": "medium", 
        "difficulty": "hard",
        "bbox": {"min": [-0.033, -0.0615, -0.033], "max": [0.033, 0.0615, 0.033]},
        "density": 50
    },
    "opened_redbull_can": {
        "category": "can_opened", 
        "size": "small", 
        "difficulty": "hard",
        "bbox": {"min": [-0.02925, -0.086, -0.02925], "max": [0.02925, 0.086, 0.02925]},
        "density": 50
    },
    
    # 瓶子类
    "blue_plastic_bottle": {
        "category": "bottle", 
        "size": "large", 
        "difficulty": "hard",
        "bbox": {"min": [-0.03005, -0.1015, -0.03025], "max": [0.03005, 0.1015, 0.03025]},
        "density": 50
    },
    
    # 水果类
    "apple": {
        "category": "fruit", 
        "size": "medium", 
        "difficulty": "easy",
        "bbox": {"min": [-0.04, -0.04, -0.04], "max": [0.04, 0.04, 0.04]},
        "density": 800
    },
    "orange": {
        "category": "fruit", 
        "size": "medium", 
        "difficulty": "easy",
        "bbox": {"min": [-0.035, -0.035, -0.034], "max": [0.035, 0.035, 0.034]},
        "density": 800
    },
    
    # 蔬菜类
    "bridge_carrot_generated_modified": {
        "category": "vegetable", 
        "size": "medium", 
        "difficulty": "medium",
        "bbox": {"min": [-0.0575, -0.016, -0.019], "max": [0.0575, 0.016, 0.019]},
        "density": 700
    },
    "eggplant": {
        "category": "vegetable", 
        "size": "medium", 
        "difficulty": "medium",
        "bbox": {"min": [-0.03915, -0.0187, -0.0190], "max": [0.03915, 0.0187, 0.0190]},
        "density": 400
    },
    
    # 日用品类
    "sponge": {
        "category": "household", 
        "size": "small", 
        "difficulty": "medium",
        "bbox": {"min": [-0.035, -0.05, -0.018], "max": [0.035, 0.05, 0.018]},
        "density": 150
    },
    
    # 餐具类
    "bridge_spoon_generated_modified": {
        "category": "utensil", 
        "size": "small", 
        "difficulty": "hard",
        "bbox": {"min": [-0.0173, -0.05, -0.013021], "max": [0.0173, 0.05, 0.013021]},
        "density": 1200
    },
    
    # 几何体类
    "green_cube_3cm": {
        "category": "cube", 
        "size": "small", 
        "difficulty": "easy",
        "bbox": {"min": [-0.015, -0.015, -0.015], "max": [0.015, 0.015, 0.015]},
        "density": 1000
    },
    "yellow_cube_3cm": {
        "category": "cube", 
        "size": "small", 
        "difficulty": "easy",
        "bbox": {"min": [-0.015, -0.015, -0.015], "max": [0.015, 0.015, 0.015]},
        "density": 1000
    }
}


# 光照配置库
LIGHTING_CONFIGS = {
    "indoor_bright": {
        "description": "明亮的室内光照，适合清晰观察",
        "ambient_light": [0.4, 0.4, 0.4],
        "directional_lights": [
            {"direction": [0, 0, -1], "color": [2.0, 2.0, 2.0], "shadow": True},
            {"direction": [1, 1, -1], "color": [0.8, 0.8, 0.8], "shadow": False}
        ]
    },

    "laboratory": {
        "description": "实验室标准光照",
        "ambient_light": [0.3, 0.3, 0.3],
        "directional_lights": [
            {"direction": [1, 1, -1], "color": [1.5, 1.5, 1.5], "shadow": True}
        ]
    },

    "laboratory_precise": {
        "description": "实验室精密光照，减少阴影",
        "ambient_light": [0.5, 0.5, 0.5],
        "directional_lights": [
            {"direction": [0, 0, -1], "color": [1.0, 1.0, 1.0], "shadow": False},
            {"direction": [1, 0, -0.5], "color": [0.5, 0.5, 0.5], "shadow": False},
            {"direction": [-1, 0, -0.5], "color": [0.5, 0.5, 0.5], "shadow": False}
        ]
    },

    "natural": {
        "description": "自然光照，模拟真实环境",
        "ambient_light": [0.2, 0.2, 0.2],
        "directional_lights": [
            {"direction": [1, 0.5, -1], "color": [1.8, 1.6, 1.4], "shadow": True}
        ]
    },

    "industrial": {
        "description": "工业环境光照",
        "ambient_light": [0.25, 0.25, 0.25],
        "directional_lights": [
            {"direction": [0, 0, -1], "color": [2.5, 2.5, 2.5], "shadow": True},
            {"direction": [-1, -1, -1], "color": [0.5, 0.5, 0.5], "shadow": False}
        ]
    }
}


def setup_environment():
    """设置SimplerEnv环境"""
    try:
        import sapien.core as sapien
        sapien.render_config.rt_use_denoiser = False
        return True
    except ImportError as e:
        print(f"❌ 环境设置失败: {e}")
        return False


# ==================== 配置验证和管理 ====================

class ConfigValidator:
    """配置验证器"""

    @staticmethod
    def validate_objects(objects: List[str]) -> Tuple[bool, str]:
        """验证物体列表"""
        if not objects:
            return False, "物体列表不能为空"

        invalid_objects = [obj for obj in objects if obj not in AVAILABLE_OBJECTS]
        if invalid_objects:
            available = list(AVAILABLE_OBJECTS.keys())
            return False, f"未知物体: {invalid_objects}。可用物体: {available}"

        return True, "物体验证通过"

    @staticmethod
    def validate_robot_type(robot_type: str) -> Tuple[bool, str]:
        """验证机器人类型"""
        valid_robots = ["google_robot_static", "widowx", "panda"]
        if robot_type not in valid_robots:
            return False, f"未知机器人类型: {robot_type}。可用类型: {valid_robots}"
        return True, "机器人类型验证通过"

    @staticmethod
    def validate_camera_type(camera_type: str, robot_type: str = None) -> Tuple[bool, str]:
        """验证相机类型与机器人的兼容性"""
        # 机器人与相机的兼容性映射
        robot_camera_map = {
            "google_robot_static": ["overhead_camera"],
            "widowx": ["base_camera", "3rd_view_camera"],
            "panda": ["hand_camera"]
        }

        if robot_type and robot_type in robot_camera_map:
            valid_cameras = robot_camera_map[robot_type]
            if camera_type not in valid_cameras:
                return False, f"机器人 {robot_type} 不支持相机 {camera_type}。支持的相机: {valid_cameras}"
        else:
            # 向后兼容：如果没有指定机器人类型，使用原来的验证
            valid_cameras = ["overhead_camera", "base_camera", "3rd_view_camera", "hand_camera"]
            if camera_type not in valid_cameras:
                return False, f"未知相机类型: {camera_type}。可用类型: {valid_cameras}"

        return True, "相机类型验证通过"

    @staticmethod
    def get_robot_camera_mapping(robot_type: str, camera_type: str) -> str:
        """获取机器人的实际相机名称映射"""
        camera_mapping = {
            "google_robot_static": {
                "overhead_camera": "overhead_camera"
            },
            "widowx": {
                "base_camera": "3rd_view_camera",
                "3rd_view_camera": "3rd_view_camera"
            },
            "panda": {
                "hand_camera": "hand_camera"
            }
        }

        if robot_type in camera_mapping and camera_type in camera_mapping[robot_type]:
            return camera_mapping[robot_type][camera_type]
        return camera_type  # 默认返回原始名称

    @staticmethod
    def get_robot_control_mode(robot_type: str) -> str:
        """获取机器人的默认控制模式"""
        control_modes = {
            "google_robot_static": "arm_pd_ee_delta_pose_gripper_pd_joint_pos",
            "widowx": "arm_pd_ee_target_delta_pose_align2_gripper_pd_joint_pos",
            "panda": "pd_ee_delta_pose"
        }
        return control_modes.get(robot_type, "arm_pd_ee_delta_pose_gripper_pd_joint_pos")

    @staticmethod
    def validate_lighting_mode(lighting_mode: str) -> Tuple[bool, str]:
        """验证光照模式"""
        if lighting_mode not in LIGHTING_CONFIGS:
            available = list(LIGHTING_CONFIGS.keys())
            return False, f"未知光照模式: {lighting_mode}。可用模式: {available}"
        return True, "光照模式验证通过"

    @staticmethod
    def validate_compatibility(robot_type: str, camera_type: str, objects: List[str]) -> Tuple[bool, str]:
        """验证配置兼容性"""
        # 检查机器人和相机兼容性
        if robot_type == "widowx" and camera_type == "overhead_camera":
            return False, "WidowX机器人建议使用base_camera以获得更好的视角"

        # 检查物体数量限制
        if robot_type == "widowx" and len(objects) > 6:
            return False, "WidowX机器人建议物体数量不超过6个"

        if robot_type.startswith("google_robot") and len(objects) > 8:
            return False, "Google Robot建议物体数量不超过8个"

        return True, "配置兼容性验证通过"


class SceneConfigManager:
    """场景配置管理器"""

    @staticmethod
    def get_preset_config(preset: Union[str, PresetType]) -> SceneConfig:
        """获取预设配置"""
        if isinstance(preset, str):
            preset = PresetType(preset)

        if preset not in PRESET_CONFIGS:
            raise ValueError(f"未知预设: {preset}")

        return PRESET_CONFIGS[preset]

    @staticmethod
    def list_presets() -> Dict[str, str]:
        """列出所有预设配置"""
        return {preset.value: config.description for preset, config in PRESET_CONFIGS.items()}

    @staticmethod
    def recommend_objects(preset: Union[str, PresetType], num_objects: int = None) -> List[str]:
        """推荐物体组合"""
        config = SceneConfigManager.get_preset_config(preset)

        if num_objects is None:
            num_objects = min(len(config.recommended_objects), config.max_objects)

        num_objects = min(num_objects, len(config.recommended_objects), config.max_objects)

        return config.recommended_objects[:num_objects]

    @staticmethod
    def get_lighting_config(mode: str) -> Dict:
        """获取光照配置"""
        if mode not in LIGHTING_CONFIGS:
            raise ValueError(f"未知光照模式: {mode}")

        return LIGHTING_CONFIGS[mode].copy()

    @staticmethod
    def suggest_workspace_bounds(robot_type: str, num_objects: int) -> Tuple[Tuple[float, float], Tuple[float, float]]:
        """建议工作空间边界"""
        if robot_type == "widowx":
            # WidowX有较小的工作空间
            if num_objects <= 3:
                return ((-0.25, 0.05), (-0.10, 0.10))
            else:
                return ((-0.30, 0.10), (-0.15, 0.15))
        else:
            # Google Robot有较大的工作空间
            if num_objects <= 4:
                return ((-0.28, -0.22), (0.12, 0.18))
            elif num_objects <= 6:
                return ((-0.30, -0.20), (0.10, 0.20))
            else:
                return ((-0.32, -0.18), (0.08, 0.22))


# ==================== 智能物体放置算法 ====================

class SmartObjectPlacer:
    """智能物体放置器"""

    def __init__(self, workspace_bounds: Tuple[Tuple[float, float], Tuple[float, float]],
                 table_height: float = 0.87, min_spacing: float = 0.03):
        self.workspace_bounds = workspace_bounds
        self.table_height = table_height
        self.min_spacing = min_spacing

        # 生成安全位置网格
        self.safe_positions = self._generate_safe_grid()

    def _generate_safe_grid(self) -> List[Tuple[float, float]]:
        """生成安全位置网格"""
        x_min, x_max = self.workspace_bounds[0]
        y_min, y_max = self.workspace_bounds[1]

        # 计算网格点数量
        x_range = x_max - x_min
        y_range = y_max - y_min

        # 根据工作空间大小调整网格密度
        x_steps = max(3, int(x_range / self.min_spacing))
        y_steps = max(3, int(y_range / self.min_spacing))

        positions = []
        for i in range(x_steps):
            for j in range(y_steps):
                x = x_min + (x_range * i) / (x_steps - 1)
                y = y_min + (y_range * j) / (y_steps - 1)
                positions.append((x, y))

        # 按距离中心的远近排序，优先使用中心位置
        center_x = (x_min + x_max) / 2
        center_y = (y_min + y_max) / 2

        positions.sort(key=lambda pos: (pos[0] - center_x)**2 + (pos[1] - center_y)**2)

        return positions

    def get_positions_for_objects(self, num_objects: int) -> List[Tuple[float, float]]:
        """为指定数量的物体获取位置"""
        if num_objects > len(self.safe_positions):
            # 如果需要的位置超过网格数量，生成随机位置
            extra_positions = self._generate_random_positions(num_objects - len(self.safe_positions))
            return self.safe_positions + extra_positions

        return self.safe_positions[:num_objects]

    def _generate_random_positions(self, num_positions: int) -> List[Tuple[float, float]]:
        """生成随机位置（作为备用）"""
        x_min, x_max = self.workspace_bounds[0]
        y_min, y_max = self.workspace_bounds[1]

        positions = []
        for _ in range(num_positions):
            x = random.uniform(x_min, x_max)
            y = random.uniform(y_min, y_max)
            positions.append((x, y))

        return positions

    def validate_position(self, pos: Tuple[float, float]) -> bool:
        """验证位置是否在工作空间内"""
        x, y = pos
        x_min, x_max = self.workspace_bounds[0]
        y_min, y_max = self.workspace_bounds[1]

        return x_min <= x <= x_max and y_min <= y <= y_max


def create_stable_environment(target_object, distractor_objects, robot_type="google_robot_static",
                             lighting_config=None, object_positions=None, workspace_bounds=None,
                             enable_debug_render=False):
    """创建稳定的多物体环境，支持光照和位置控制"""
    from mani_skill2_real2sim.envs.custom_scenes.grasp_single_in_scene import GraspSingleCustomInSceneEnv
    import sapien.core as sapien
    import numpy as np

    class StableMultiObjectEnv(GraspSingleCustomInSceneEnv):
        def __init__(self, target_id, distractor_ids, lighting_config=None, object_positions=None,
                     workspace_bounds=None, enable_debug_render=False, **kwargs):
            kwargs.setdefault("model_ids", [target_id])
            kwargs.setdefault("distractor_model_ids", distractor_ids)
            kwargs.setdefault("robot", robot_type)
            
            # 🔧 作者指导：添加调试渲染功能
            if enable_debug_render:
                kwargs.setdefault("rgb_overlay_path", None)
                print("🔍 启用调试渲染模式 - 将显示原始sim背景以检查穿透问题")
            
            # 保存配置
            self.lighting_config = lighting_config or {}
            self.custom_object_positions = object_positions or []
            self.workspace_bounds = workspace_bounds or ((-0.30, -0.20), (0.10, 0.20))
            self.enable_debug_render = enable_debug_render

            # 创建智能物体放置器
            self.object_placer = SmartObjectPlacer(self.workspace_bounds)

            super().__init__(**kwargs)

        def reset(self, seed=None, options=None):
            if options is None:
                options = dict()
            options = options.copy()
            options["distractor_model_ids"] = self.distractor_model_ids
            return super().reset(seed=seed, options=options)

        def _setup_lighting(self):
            """设置自定义光照"""
            if self.bg_name is not None:
                return

            # 如果没有自定义光照配置，使用默认设置
            if not self.lighting_config:
                super()._setup_lighting()
                return

            print(f"🔆 应用自定义光照配置")

            # 设置环境光
            ambient_light = self.lighting_config.get('ambient_light', [0.3, 0.3, 0.3])
            self._scene.set_ambient_light(ambient_light)
            print(f"   - 环境光: RGB({ambient_light[0]:.2f}, {ambient_light[1]:.2f}, {ambient_light[2]:.2f})")

            # 添加方向光
            directional_lights = self.lighting_config.get('directional_lights', [])
            if not directional_lights:
                # 默认方向光
                directional_lights = [{
                    'direction': [1, 1, -1],
                    'color': [1.0, 1.0, 1.0],
                    'shadow': self.lighting_config.get('enable_shadow', True)
                }]

            for i, light in enumerate(directional_lights):
                direction = light.get('direction', [1, 1, -1])
                color = light.get('color', [1.0, 1.0, 1.0])
                shadow = light.get('shadow', False)
                scale = light.get('scale', 5)
                shadow_map_size = light.get('shadow_map_size', 2048)

                try:
                    if shadow and i == 0:  # 只有第一个光源启用阴影
                        self._scene.add_directional_light(
                            direction, color, shadow=True, scale=scale, shadow_map_size=shadow_map_size
                        )
                        print(f"   - 方向光{i+1}: 方向{direction}, 颜色{color}, 阴影=是")
                    else:
                        self._scene.add_directional_light(direction, color, shadow=False)
                        print(f"   - 方向光{i+1}: 方向{direction}, 颜色{color}, 阴影=否")
                except Exception as e:
                    print(f"   ⚠️ 添加方向光{i+1}失败: {e}")
                    # 降级为无阴影光源
                    try:
                        self._scene.add_directional_light(direction, color, shadow=False)
                        print(f"   - 方向光{i+1}: 降级为无阴影")
                    except Exception as e2:
                        print(f"   ❌ 完全无法添加方向光{i+1}: {e2}")

        def _get_object_safe_height(self, obj_name):
            """获取物体的安全高度，避免穿透问题"""
            # 根据物体类型设置不同的安全高度
            if obj_name in AVAILABLE_OBJECTS:
                obj_info = AVAILABLE_OBJECTS[obj_name]
                bbox = obj_info.get('bbox', {})
                if bbox:
                    # 使用物体的实际高度信息
                    obj_height = bbox['max'][2] - bbox['min'][2]
                    # 安全高度 = 桌面高度 + 物体高度的一半 + 小偏移
                    safe_height = self.scene_table_height + obj_height / 2 + 0.01
                    return safe_height
            
            # 默认安全高度
            return self.scene_table_height + 0.03

        def _check_object_penetration(self, obj, expected_pos):
            """检查物体是否出现穿透问题"""
            actual_pos = obj.pose.p
            
            # 检查是否掉落到桌面以下
            if actual_pos[2] < self.scene_table_height - 0.02:
                return True, f"物体掉落到桌面以下: {actual_pos[2]:.3f} < {self.scene_table_height:.3f}"
            
            # 检查是否偏离预期位置太远
            distance = np.linalg.norm(actual_pos[:2] - expected_pos[:2])
            if distance > 0.15:  # 15cm的容差
                return True, f"物体偏离预期位置太远: 距离={distance:.3f}m"
            
            return False, "物体位置正常"

        def _initialize_actors(self):
            """🔧 改进的物体初始化方法 - 遵循作者指导，减少穿透问题"""
            from transforms3d.euler import euler2quat
            import sapien.core as sapien

            # 收集所有物体
            all_objects = [self.obj]
            if hasattr(self, 'distractor_objs') and self.distractor_objs:
                all_objects.extend(self.distractor_objs)

            print(f"🔧 改进的物体初始化 - 总共 {len(all_objects)} 个物体")
            if self.enable_debug_render:
                print("🔍 调试模式已启用 - 将显示原始渲染以检查穿透")

            # 🎯 第一步：将机器人移到远处，避免初始化时的碰撞
            self.agent.robot.set_pose(sapien.Pose([-10, 0, 0]))
            print("🤖 机器人已移动到远处")

            # 🎯 第二步：获取安全位置
            if self.custom_object_positions and len(self.custom_object_positions) >= len(all_objects):
                print(f"📍 使用自定义物体位置")
                positions = []
                for i, pos in enumerate(self.custom_object_positions[:len(all_objects)]):
                    if len(pos) == 2:
                        positions.append((pos[0], pos[1]))
                    elif len(pos) == 3:
                        positions.append((pos[0], pos[1]))
                    else:
                        positions.append((-0.25, 0.20))
            else:
                print(f"📍 使用预定义安全位置")
                # 🔧 根据作者指导，使用更保守的位置，确保不会发生穿透
                safe_positions = [
                    (-0.25, 0.15),  # 中心位置
                    (-0.22, 0.18),  # 右上
                    (-0.28, 0.12),  # 左下
                    (-0.25, 0.21),  # 上方
                    (-0.25, 0.09),  # 下方
                    (-0.19, 0.15),  # 右侧
                    (-0.31, 0.15),  # 左侧
                    (-0.22, 0.21),  # 右上角
                    (-0.28, 0.09),  # 左下角
                ]
                positions = [(x, y) for x, y in safe_positions[:len(all_objects)]]

            print(f"📍 将使用的位置: {positions}")

            # 🎯 第三步：逐个放置物体，使用改进的方法
            placement_results = []
            
            for i, obj in enumerate(all_objects):
                if obj is None:
                    print(f"⚠️ 第{i+1}个物体为None，跳过")
                    continue

                obj_name = obj.name
                print(f"\n🔧 正在放置物体 {i+1}/{len(all_objects)}: {obj_name}")

                # 获取位置
                if i < len(positions):
                    x, y = positions[i]
                else:
                    x, y = -0.25, 0.15  # 备用中心位置

                # 🔧 关键改进：使用物体特定的安全高度
                safe_z = self._get_object_safe_height(obj_name)
                expected_pos = np.array([x, y, safe_z])

                print(f"   📍 目标位置: ({x:.3f}, {y:.3f}, {safe_z:.3f})")

                # 🔧 关键改进：在放置前清理物体状态
                obj.set_velocity(np.zeros(3))
                obj.set_angular_velocity(np.zeros(3))
                
                # 设置稳定的姿态（不使用随机旋转）
                stable_quat = euler2quat(0, 0, 0)
                
                # 🔧 关键改进：直接放置到安全高度，不从高处落下
                obj.set_pose(sapien.Pose(expected_pos, stable_quat))
                
                # 设置物理属性以提高稳定性
                obj.set_damping(0.5, 0.5)  # 线性和角度阻尼
                
                # 短暂稳定
                self._settle(0.1)
                
                # 🔧 关键改进：检查放置结果
                has_penetration, msg = self._check_object_penetration(obj, expected_pos)
                actual_pos = obj.pose.p
                
                placement_result = {
                    'object': obj_name,
                    'expected_pos': expected_pos,
                    'actual_pos': actual_pos,
                    'has_penetration': has_penetration,
                    'message': msg
                }
                placement_results.append(placement_result)
                
                if has_penetration:
                    print(f"   ❌ 检测到穿透问题: {msg}")
                    print(f"   🔧 执行单次修正...")
                    
                    # 🔧 简化修正：只做一次修正，避免复杂的多轮修正
                    obj.set_velocity(np.zeros(3))
                    obj.set_angular_velocity(np.zeros(3))
                    # 稍微提高高度
                    corrected_pos = expected_pos.copy()
                    corrected_pos[2] += 0.02  # 额外提高2cm
                    obj.set_pose(sapien.Pose(corrected_pos, stable_quat))
                    self._settle(0.2)
                    
                    # 再次检查
                    has_penetration_after, msg_after = self._check_object_penetration(obj, corrected_pos)
                    actual_pos_after = obj.pose.p
                    
                    if has_penetration_after:
                        print(f"   ⚠️ 修正后仍有问题: {msg_after}")
                        print(f"   💡 建议：检查物体 {obj_name} 的模型或调整位置")
                    else:
                        print(f"   ✅ 修正成功: 位置 ({actual_pos_after[0]:.3f}, {actual_pos_after[1]:.3f}, {actual_pos_after[2]:.3f})")
                        placement_result['corrected'] = True
                        placement_result['actual_pos'] = actual_pos_after
                else:
                    print(f"   ✅ 放置成功: 位置 ({actual_pos[0]:.3f}, {actual_pos[1]:.3f}, {actual_pos[2]:.3f})")

            # 🎯 第四步：最终稳定
            print(f"\n🔄 最终稳定所有物体...")
            self._settle(0.5)  # 稍长的稳定时间

            # 🎯 第五步：最终检查和报告
            print(f"\n📊 物体放置结果总结:")
            stable_count = 0
            problem_count = 0
            
            for i, result in enumerate(placement_results):
                obj_name = result['object']
                actual_pos = result['actual_pos']
                
                # 最终检查
                final_check = actual_pos[2] >= self.scene_table_height - 0.02
                
                if final_check:
                    stable_count += 1
                    status = "✅ 稳定"
                else:
                    problem_count += 1
                    status = "❌ 问题"
                
                print(f"   {i+1}. {obj_name}: ({actual_pos[0]:.3f}, {actual_pos[1]:.3f}, {actual_pos[2]:.3f}) {status}")
                
                if result['has_penetration']:
                    print(f"      ⚠️ 初始穿透: {result['message']}")

            print(f"\n🎯 初始化完成统计:")
            print(f"   ✅ 稳定物体: {stable_count}/{len(all_objects)}")
            print(f"   ❌ 问题物体: {problem_count}/{len(all_objects)}")
            print(f"   📈 成功率: {stable_count/len(all_objects)*100:.1f}%")
            
            if self.enable_debug_render:
                print(f"\n🔍 调试提示:")
                print(f"   - 如果看到物体穿透或异常移动，请检查:")
                print(f"   - 物体模型是否正确")
                print(f"   - 初始位置是否合理")
                print(f"   - 桌面高度是否准确")
                print(f"   - 当前桌面高度: {self.scene_table_height:.3f}")

    return StableMultiObjectEnv


# ==================== 自定义物体位置功能 ====================

def create_scene_with_custom_positions(
    objects: List[str],
    positions: List[Tuple[float, float, float]],
    robot_type: str = "google_robot_static",
    camera_type: str = "overhead_camera",
    lighting_mode: str = "indoor_bright",
    output_dir: str = "/home/<USER>/claude/SpatialVLA/Z/Z_new_trial/result",
    image_prefix: str = "custom_positions",
    num_episodes: int = 1,
    enable_debug_render: bool = False,  # 🔧 新增：启用调试渲染模式
    verbose: bool = True
) -> Tuple[bool, Dict, List[str]]:
    """
    🎯 自定义物体位置接口 - 精确控制每个物体的位置

    参数:
        objects: 物体列表，第一个为目标物体，其余为干扰物体
        positions: 物体位置列表 [(x1, y1, z1), (x2, y2, z2), ...]
                  如果z为None或0，将自动设置为桌面高度+0.02
        robot_type: 机器人类型
        camera_type: 相机类型
        lighting_mode: 光照模式
        output_dir: 输出目录
        image_prefix: 图像前缀
        num_episodes: 生成场景数量
        enable_debug_render: 是否启用调试渲染模式（显示原始sim背景）
        verbose: 是否显示详细信息

    返回:
        (success, results, image_paths)

    示例:
        objects = ["apple", "orange", "coke_can"]
        positions = [
            (-0.25, 0.15, 0),    # apple在中心
            (-0.22, 0.18, 0),    # orange在右上
            (-0.28, 0.12, 0)     # coke_can在左下
        ]
        success, results, images = create_scene_with_custom_positions(
            objects, positions, enable_debug_render=True
        )
    """

    if verbose:
        print("🎯 SimplerEnv自定义位置接口")
        print("=" * 50)

    try:
        # 验证参数
        if len(objects) != len(positions):
            error_msg = f"物体数量({len(objects)})与位置数量({len(positions)})不匹配"
            if verbose:
                print(f"❌ {error_msg}")
            return False, {"error": error_msg}, []

        # 验证物体
        validator = ConfigValidator()
        valid, msg = validator.validate_objects(objects)
        if not valid:
            if verbose:
                print(f"❌ {msg}")
            return False, {"error": msg}, []

        # 处理位置 - 自动设置z坐标
        processed_positions = []
        for i, (x, y, z) in enumerate(positions):
            if z is None or z == 0:
                z = 0.87 + 0.02  # 桌面高度 + 小偏移
            processed_positions.append((x, y, z))

            if verbose:
                print(f"   📍 {objects[i]}: ({x:.2f}, {y:.2f}, {z:.2f})")

        # 验证位置是否在合理范围内
        for i, (x, y, z) in enumerate(processed_positions):
            if not (-0.5 <= x <= 0.5 and -0.5 <= y <= 0.5 and 0.8 <= z <= 1.2):
                warning_msg = f"物体 {objects[i]} 位置 ({x:.2f}, {y:.2f}, {z:.2f}) 可能超出合理范围"
                if verbose:
                    print(f"⚠️ {warning_msg}")

        if verbose:
            print(f"🤖 机器人: {robot_type}")
            print(f"📷 相机: {camera_type}")
            print(f"🔆 光照: {lighting_mode}")
            print(f"🔍 调试渲染: {enable_debug_render}")
            print("=" * 50)

        # 调用高级接口
        return create_scene_advanced({
            "target_object": objects[0],
            "distractor_objects": objects[1:] if len(objects) > 1 else [],
            "object_positions": processed_positions,
            "robot_type": robot_type,
            "camera_type": camera_type,
            "lighting_mode": lighting_mode,
            "num_episodes": num_episodes,
            "output_dir": output_dir,
            "image_prefix": image_prefix,
            "enable_debug_render": enable_debug_render,  # 🔧 传递调试渲染参数
            "verbose": verbose,
            "enable_stable_placement": True
        })

    except Exception as e:
        if verbose:
            print(f"❌ 自定义位置接口执行失败: {e}")
        return False, {"error": str(e)}, []


def generate_grid_positions(
    num_objects: int,
    workspace_bounds: Tuple[Tuple[float, float], Tuple[float, float]] = ((-0.35, -0.15), (0.05, 0.35)),
    z_height: float = 0.0
) -> List[Tuple[float, float, float]]:
    """
    🔧 生成网格布局的物体位置

    参数:
        num_objects: 物体数量
        workspace_bounds: 工作空间边界 ((x_min, x_max), (y_min, y_max))
        z_height: z坐标高度，0表示自动设置为桌面高度

    返回:
        位置列表 [(x1, y1, z1), (x2, y2, z2), ...]
    """
    import math

    # 计算网格尺寸
    cols = math.ceil(math.sqrt(num_objects))
    rows = math.ceil(num_objects / cols)

    x_min, x_max = workspace_bounds[0]
    y_min, y_max = workspace_bounds[1]

    x_step = (x_max - x_min) / (cols + 1)
    y_step = (y_max - y_min) / (rows + 1)

    positions = []
    for i in range(num_objects):
        row = i // cols
        col = i % cols

        x = x_min + (col + 1) * x_step
        y = y_min + (row + 1) * y_step
        z = z_height

        positions.append((x, y, z))

    return positions


def generate_circle_positions(
    num_objects: int,
    center: Tuple[float, float] = (-0.25, 0.20),
    radius: float = 0.08,
    z_height: float = 0.0
) -> List[Tuple[float, float, float]]:
    """
    🔧 生成圆形布局的物体位置

    参数:
        num_objects: 物体数量
        center: 圆心位置 (x, y)
        radius: 圆半径
        z_height: z坐标高度，0表示自动设置为桌面高度

    返回:
        位置列表 [(x1, y1, z1), (x2, y2, z2), ...]
    """
    import math

    positions = []
    center_x, center_y = center

    if num_objects == 1:
        # 单个物体放在中心
        positions.append((center_x, center_y, z_height))
    else:
        # 多个物体围成圆形
        angle_step = 2 * math.pi / num_objects
        for i in range(num_objects):
            angle = i * angle_step
            x = center_x + radius * math.cos(angle)
            y = center_y + radius * math.sin(angle)
            positions.append((x, y, z_height))

    return positions


# ==================== 分层接口设计 ====================

def create_scene_simple(
    preset: Union[str, PresetType] = "desktop_picking",
    objects: Optional[List[str]] = None,
    num_objects: Optional[int] = None,
    num_episodes: int = 1,
    output_dir: str = "/home/<USER>/claude/SpatialVLA/Z/Z_new_trial/result",
    enable_debug_render: bool = False,  # 🔧 新增：启用调试渲染模式
    verbose: bool = True
) -> Tuple[bool, Dict, List[str]]:
    """
    🚀 简单接口 - 一键生成场景，适合新手用户

    参数:
        preset: 预设配置类型 ("desktop_picking", "multi_object_sorting", "bridge_dataset", etc.)
        objects: 自定义物体列表，如果为None则使用预设推荐物体
        num_objects: 物体数量，如果为None则使用预设推荐数量
        num_episodes: 生成场景数量
        output_dir: 输出目录
        enable_debug_render: 是否启用调试渲染模式（显示原始sim背景）
        verbose: 是否显示详细信息

    返回:
        (success, results, image_paths)
    """

    if verbose:
        print("🚀 SimplerEnv简单接口 - 一键生成场景")
        print("=" * 50)

    try:
        # 获取预设配置
        config = SceneConfigManager.get_preset_config(preset)

        if verbose:
            print(f"📋 使用预设: {config.name}")
            print(f"   📝 描述: {config.description}")

        # 确定物体列表
        if objects is None:
            if num_objects is None:
                num_objects = min(len(config.recommended_objects), config.max_objects)
            objects = SceneConfigManager.recommend_objects(preset, num_objects)

        # 验证配置
        validator = ConfigValidator()

        # 验证物体
        valid, msg = validator.validate_objects(objects)
        if not valid:
            if verbose:
                print(f"❌ 物体验证失败: {msg}")
            return False, {"error": msg}, []

        # 验证兼容性
        valid, msg = validator.validate_compatibility(config.robot_type, config.camera_type, objects)
        if not valid:
            if verbose:
                print(f"⚠️ 兼容性警告: {msg}")

        if verbose:
            print(f"🎯 目标物体: {objects[0]}")
            print(f"🎯 干扰物体: {objects[1:] if len(objects) > 1 else '无'}")
            print(f"🤖 机器人: {config.robot_type}")
            print(f"📷 相机: {config.camera_type}")
            print(f"🔆 光照: {config.lighting_mode}")
            print(f"🔍 调试渲染: {enable_debug_render}")
            print("=" * 50)

        # 调用高级接口
        return create_scene_advanced({
            "target_object": objects[0],
            "distractor_objects": objects[1:] if len(objects) > 1 else [],
            "robot_type": config.robot_type,
            "camera_type": config.camera_type,
            "lighting_mode": config.lighting_mode,
            "workspace_bounds": config.workspace_bounds,
            "control_frequency": config.control_frequency,
            "num_episodes": num_episodes,
            "output_dir": output_dir,
            "image_prefix": f"simple_{preset}",  # 添加唯一前缀
            "enable_debug_render": enable_debug_render,  # 🔧 传递调试渲染参数
            "verbose": verbose,
            "enable_stable_placement": True
        })

    except Exception as e:
        if verbose:
            print(f"❌ 简单接口执行失败: {e}")
        return False, {"error": str(e)}, []


def create_scene_custom(
    robot_type: str,
    camera_type: str,
    lighting_mode: str,
    objects: List[str],
    workspace_bounds: Optional[Tuple[Tuple[float, float], Tuple[float, float]]] = None,
    num_episodes: int = 1,
    output_dir: str = "/home/<USER>/claude/SpatialVLA/Z/Z_new_trial/result",
    enable_debug_render: bool = False,  # 🔧 新增：启用调试渲染模式
    verbose: bool = True,
    **kwargs
) -> Tuple[bool, Dict, List[str]]:
    """
    🎛️ 中级接口 - 自定义配置生成场景，适合有经验用户

    参数:
        robot_type: 机器人类型 ("google_robot_static", "google_robot_mobile", "widowx")
        camera_type: 相机类型 ("overhead_camera", "base_camera")
        lighting_mode: 光照模式 ("indoor_bright", "laboratory", "natural", etc.)
        objects: 物体列表，第一个为目标物体，其余为干扰物体
        workspace_bounds: 工作空间边界，如果为None则自动推荐
        num_episodes: 生成场景数量
        output_dir: 输出目录
        enable_debug_render: 是否启用调试渲染模式（显示原始sim背景）
        verbose: 是否显示详细信息
        **kwargs: 其他高级参数

    返回:
        (success, results, image_paths)
    """

    if verbose:
        print("🎛️ SimplerEnv中级接口 - 自定义配置")
        print("=" * 50)

    try:
        # 验证配置
        validator = ConfigValidator()

        # 验证机器人类型
        valid, msg = validator.validate_robot_type(robot_type)
        if not valid:
            if verbose:
                print(f"❌ {msg}")
            return False, {"error": msg}, []

        # 验证相机类型
        valid, msg = validator.validate_camera_type(camera_type)
        if not valid:
            if verbose:
                print(f"❌ {msg}")
            return False, {"error": msg}, []

        # 验证光照模式
        valid, msg = validator.validate_lighting_mode(lighting_mode)
        if not valid:
            if verbose:
                print(f"❌ {msg}")
            return False, {"error": msg}, []

        # 验证物体
        valid, msg = validator.validate_objects(objects)
        if not valid:
            if verbose:
                print(f"❌ {msg}")
            return False, {"error": msg}, []

        # 验证兼容性
        valid, msg = validator.validate_compatibility(robot_type, camera_type, objects)
        if not valid:
            if verbose:
                print(f"⚠️ 兼容性警告: {msg}")

        # 自动推荐工作空间边界
        if workspace_bounds is None:
            workspace_bounds = SceneConfigManager.suggest_workspace_bounds(robot_type, len(objects))
            if verbose:
                print(f"📍 自动推荐工作空间: {workspace_bounds}")

        if verbose:
            print(f"🎯 目标物体: {objects[0]}")
            print(f"🎯 干扰物体: {objects[1:] if len(objects) > 1 else '无'}")
            print(f"🤖 机器人: {robot_type}")
            print(f"📷 相机: {camera_type}")
            print(f"🔆 光照: {lighting_mode}")
            print(f"🔍 调试渲染: {enable_debug_render}")
            print("=" * 50)

        # 调用高级接口
        return create_scene_advanced({
            "target_object": objects[0],
            "distractor_objects": objects[1:] if len(objects) > 1 else [],
            "robot_type": robot_type,
            "camera_type": camera_type,
            "lighting_mode": lighting_mode,
            "workspace_bounds": workspace_bounds,
            "num_episodes": num_episodes,
            "output_dir": output_dir,
            "image_prefix": f"custom_{robot_type}_{camera_type}",  # 添加唯一前缀
            "enable_debug_render": enable_debug_render,  # 🔧 传递调试渲染参数
            "verbose": verbose,
            "enable_stable_placement": True,
            **kwargs
        })

    except Exception as e:
        if verbose:
            print(f"❌ 中级接口执行失败: {e}")
        return False, {"error": str(e)}, []


def create_scene_advanced(config: Dict) -> Tuple[bool, Dict, List[str]]:
    """
    🔧 高级接口 - 完全自定义配置，适合专家用户

    参数:
        config: 完整配置字典

    返回:
        (success, results, image_paths)
    """
    # 这里调用原来的main函数，但使用新的配置系统
    return main(**config)


def main(
    # 🎯 物体配置
    target_object="blue_plastic_bottle",
    distractor_objects=["coke_can", "green_cube_3cm", "orange"],
    available_objects=None,  # 可选物体池，如果提供则随机选择
    num_objects_range=(3, 8),  # 物体数量范围 (最小, 最大)

    # 📍 物体位置配置
    object_positions=None,  # 自定义物体位置列表 [(x1,y1), (x2,y2), ...]
    position_mode="safe_grid",  # "safe_grid", "random", "custom"
    workspace_bounds=((-0.32, -0.18), (0.08, 0.32)),  # ((x_min, x_max), (y_min, y_max))

    # 🔆 光照配置
    lighting_config=None,  # 自定义光照配置字典
    lighting_mode="indoor_bright",  # 使用新的光照模式
    ambient_light=(0.3, 0.3, 0.3),  # 环境光RGB
    directional_lights=None,  # 方向光配置列表
    enable_shadow=True,  # 是否启用阴影

    # 📷 相机配置
    camera_type="overhead_camera",  # overhead_camera(推荐) / base_camera
    obs_mode="image",

    # 🤖 机器人配置
    robot_type="google_robot_static",
    control_mode="arm_pd_ee_delta_pose_gripper_pd_joint_pos",
    control_frequency=3,  # 添加控制频率参数

    # 📁 输出配置
    output_dir="/home/<USER>/claude/SpatialVLA/Z/Z_new_trial/result",
    image_prefix="custom_scene",
    save_images=True,

    # 🔧 运行配置
    num_episodes=3,
    render_mode="rgb_array",

    # 🎛️ 高级配置
    enable_stable_placement=True,
    enable_debug_render=False,  # 🔧 新增：启用调试渲染模式
    verbose=True
):
    """
    SimplerEnv多物体场景生成主函数
    
    🎯 物体配置参数:
    - target_object: 目标物体类型 (如: "apple", "orange", "coke_can")
    - distractor_objects: 干扰物体列表 (如: ["pepsi_can", "green_cube_3cm"])
    
    📷 相机配置参数:
    - camera_type: 相机类型
      * "overhead_camera": 顶部俯视相机(推荐，能看到所有物体)
      * "base_camera": 侧面相机(可能被遮挡)
    
    📁 输出配置参数:
    - output_dir: 图像保存目录
    - image_prefix: 图像文件名前缀
    - save_images: 是否保存图像
    
    🔧 运行配置参数:
    - num_episodes: 生成场景数量
    - enable_stable_placement: 是否启用稳定放置(防止物体掉落)
    - enable_debug_render: 是否启用调试渲染模式（显示原始sim背景）
    
    🔍 调试功能:
    - enable_debug_render=True: 启用调试渲染，显示原始sim背景以检查穿透问题
    - 根据作者指导，通过设置 rgb_overlay_path=None 来查看物体穿透情况
    
    返回:
    - success: 是否成功
    - results: 详细结果
    - image_paths: 生成的图像路径列表
    """

    # 🔧 处理物体配置
    final_target_object = target_object
    final_distractor_objects = distractor_objects.copy()

    # 如果提供了可选物体池，随机选择物体
    if available_objects:
        import random
        num_objects = random.randint(*num_objects_range)
        selected_objects = random.sample(available_objects, min(num_objects, len(available_objects)))

        if selected_objects:
            final_target_object = selected_objects[0]
            final_distractor_objects = selected_objects[1:]

        if verbose:
            print(f"🎲 从物体池随机选择: 目标={final_target_object}, 干扰物体={final_distractor_objects}")

    # 🔆 处理光照配置 - 使用新的配置系统
    final_lighting_config = lighting_config or {}

    if not lighting_config:
        # 使用新的光照配置系统
        try:
            final_lighting_config = SceneConfigManager.get_lighting_config(lighting_mode)
        except ValueError:
            # 如果光照模式不存在，使用默认配置
            if verbose:
                print(f"⚠️ 未知光照模式 '{lighting_mode}'，使用默认配置")
            final_lighting_config = SceneConfigManager.get_lighting_config("indoor_bright")

    # 📍 处理物体位置配置
    final_object_positions = object_positions

    if not object_positions and position_mode == "random":
        # 生成随机位置
        import random
        num_positions = len(final_distractor_objects) + 1
        final_object_positions = []

        for _ in range(num_positions):
            x = random.uniform(*workspace_bounds[0])
            y = random.uniform(*workspace_bounds[1])
            final_object_positions.append((x, y))

        if verbose:
            print(f"🎲 生成随机位置: {final_object_positions}")

    if verbose:
        print("🚀 SimplerEnv多物体场景生成 - 增强版")
        print("=" * 50)
        print(f"📊 配置参数:")
        print(f"   🎯 目标物体: {final_target_object}")
        print(f"   🎯 干扰物体: {final_distractor_objects}")
        print(f"   📷 相机类型: {camera_type}")
        print(f"   🤖 机器人类型: {robot_type}")
        print(f"   📁 输出目录: {output_dir}")
        print(f"   🔧 场景数量: {num_episodes}")
        print(f"   🛡️ 稳定放置: {enable_stable_placement}")
        print(f"   🔍 调试渲染: {enable_debug_render}")
        print(f"   🔆 光照模式: {lighting_mode}")
        if final_object_positions:
            print(f"   📍 自定义位置: {len(final_object_positions)} 个")
        if enable_debug_render:
            print(f"   🔍 调试模式已启用 - 将显示原始sim背景以检查穿透问题")
        print("=" * 50)
    
    try:
        # 环境设置检查
        if not setup_environment():
            return False, {"error": "环境设置失败"}, []

        # 🔧 修复相机和控制模式映射
        actual_camera_type = ConfigValidator.get_robot_camera_mapping(robot_type, camera_type)
        actual_control_mode = ConfigValidator.get_robot_control_mode(robot_type)

        if verbose:
            if actual_camera_type != camera_type:
                print(f"🔧 相机映射: {camera_type} -> {actual_camera_type}")
            if actual_control_mode != control_mode:
                print(f"🔧 控制模式: {control_mode} -> {actual_control_mode}")

        # 创建环境类
        if enable_stable_placement:
            EnvClass = create_stable_environment(
                final_target_object,
                final_distractor_objects,
                robot_type,
                lighting_config=final_lighting_config,
                object_positions=final_object_positions,
                workspace_bounds=workspace_bounds,
                enable_debug_render=enable_debug_render  # 🔧 传递调试渲染参数
            )
        else:
            from mani_skill2_real2sim.envs.custom_scenes.grasp_single_in_scene import GraspSingleCustomInSceneEnv
            EnvClass = GraspSingleCustomInSceneEnv
        
        results = {
            "episodes": [],
            "image_paths": [],
            "total_objects_expected": len(final_distractor_objects) + 1,
            "success_count": 0,
            "failure_count": 0,
            "lighting_config": final_lighting_config,
            "object_positions": final_object_positions,
            "final_target_object": final_target_object,
            "final_distractor_objects": final_distractor_objects,
            "debug_render_enabled": enable_debug_render  # 🔧 记录调试模式状态
        }
        
        # 运行多个场景
        for episode in range(num_episodes):
            if verbose:
                print(f"\n🎬 生成场景 {episode + 1}/{num_episodes}")
            
            try:
                # 创建环境参数
                env_kwargs = {
                    "target_id": final_target_object,
                    "distractor_ids": final_distractor_objects,
                    "lighting_config": final_lighting_config,
                    "object_positions": final_object_positions,
                    "workspace_bounds": workspace_bounds,
                    "enable_debug_render": enable_debug_render,  # 🔧 传递调试渲染参数
                    "obs_mode": obs_mode,
                    "control_mode": actual_control_mode,
                    "render_mode": render_mode
                }
                
                # 🔧 如果启用调试渲染，添加额外的调试信息
                if enable_debug_render:
                    env_kwargs["rgb_overlay_path"] = None  # 确保显示原始sim背景
                    if verbose:
                        print(f"🔍 调试模式: 将显示原始sim背景以检查穿透问题")
                
                # 创建环境
                env = EnvClass(**env_kwargs)
                
                # 重置环境
                obs, _ = env.reset()
                
                # 统计物体
                target_count = 1
                distractor_count = len(getattr(env.unwrapped, 'distractor_objs', []))
                total_objects = target_count + distractor_count
                
                # 检查物体位置
                actors = env.unwrapped._scene.get_all_actors()
                object_actors = [a for a in actors if a.name != 'arena']
                table_height = env.unwrapped.scene_table_height
                
                on_table_count = 0
                below_table_count = 0
                object_positions = []
                
                for actor in object_actors:
                    pos = actor.pose.p
                    object_positions.append({
                        "name": actor.name,
                        "position": [float(pos[0]), float(pos[1]), float(pos[2])],
                        "on_table": pos[2] >= table_height - 0.05
                    })
                    
                    if pos[2] >= table_height - 0.05:
                        on_table_count += 1
                    else:
                        below_table_count += 1
                
                # 🔧 调试模式下的额外信息
                if enable_debug_render and verbose:
                    print(f"🔍 调试信息:")
                    print(f"   📊 桌面高度: {table_height:.3f}")
                    print(f"   📊 物体位置详情:")
                    for obj_info in object_positions:
                        status = "✅ 桌面上" if obj_info["on_table"] else "❌ 桌面下"
                        pos = obj_info["position"]
                        print(f"      {obj_info['name']}: ({pos[0]:.3f}, {pos[1]:.3f}, {pos[2]:.3f}) {status}")
                
                # 保存图像
                episode_images = []
                if save_images and 'image' in obs:
                    for cam_name in obs['image'].keys():
                        if cam_name == actual_camera_type or camera_type == "all":
                            camera_data = obs['image'][cam_name]
                            
                            # 获取图像数据
                            rgb_img = None
                            for img_key in ['Color', 'rgb', 'color']:
                                if img_key in camera_data:
                                    rgb_img = camera_data[img_key]
                                    break
                            
                            if rgb_img is not None:
                                if rgb_img.dtype != np.uint8:
                                    rgb_img = (rgb_img * 255).astype(np.uint8)
                                
                                # 🔧 调试模式下使用特殊的文件名标识
                                debug_suffix = "_debug" if enable_debug_render else ""
                                output_path = f"{output_dir}/{image_prefix}_episode{episode+1}_{cam_name}{debug_suffix}.png"
                                cv2.imwrite(output_path, cv2.cvtColor(rgb_img, cv2.COLOR_RGB2BGR))
                                episode_images.append(output_path)
                                
                                if verbose:
                                    print(f"   💾 保存图像: {output_path}")
                
                # 记录结果
                episode_result = {
                    "episode": episode + 1,
                    "target_object": target_object,
                    "distractor_objects": distractor_objects,
                    "total_objects_expected": len(distractor_objects) + 1,
                    "total_objects_actual": total_objects,
                    "on_table_count": on_table_count,
                    "below_table_count": below_table_count,
                    "success": below_table_count == 0,
                    "object_positions": object_positions,
                    "images": episode_images,
                    "debug_render_enabled": enable_debug_render  # 🔧 记录调试模式状态
                }
                
                results["episodes"].append(episode_result)
                results["image_paths"].extend(episode_images)
                
                if episode_result["success"]:
                    results["success_count"] += 1
                    if verbose:
                        print(f"   ✅ 成功: 桌面上={on_table_count}, 掉落={below_table_count}")
                else:
                    results["failure_count"] += 1
                    if verbose:
                        print(f"   ⚠️ 部分成功: 桌面上={on_table_count}, 掉落={below_table_count}")
                        if enable_debug_render:
                            print(f"   🔍 调试提示: 检查生成的调试图像以查看穿透问题")
                
                env.close()
                
            except Exception as e:
                if verbose:
                    print(f"   ❌ 场景 {episode + 1} 失败: {e}")
                results["failure_count"] += 1
                results["episodes"].append({
                    "episode": episode + 1,
                    "success": False,
                    "error": str(e)
                })
        
        # 总结
        success_rate = results["success_count"] / num_episodes * 100
        overall_success = results["success_count"] == num_episodes
        
        if verbose:
            print(f"\n📊 生成总结:")
            print(f"   🎯 总场景数: {num_episodes}")
            print(f"   ✅ 成功场景: {results['success_count']}")
            print(f"   ❌ 失败场景: {results['failure_count']}")
            print(f"   📈 成功率: {success_rate:.1f}%")
            print(f"   🖼️ 生成图像: {len(results['image_paths'])} 张")
            
            if enable_debug_render:
                print(f"   🔍 调试模式: 已启用，图像文件名包含'_debug'后缀")
                print(f"   💡 调试提示: 如果看到物体穿透或掉落，请检查生成的调试图像")
            
            if overall_success:
                print(f"\n🎉 所有场景生成成功！")
            else:
                print(f"\n⚠️ 部分场景存在问题。")
                if not enable_debug_render:
                    print(f"💡 建议：尝试启用调试渲染模式 (enable_debug_render=True) 以查看详细问题")
        
        return overall_success, results, results["image_paths"]
        
    except Exception as e:
        if verbose:
            print(f"\n❌ 生成失败: {e}")
        return False, {"error": str(e)}, []


# ==================== 调试工具函数 ====================

def debug_object_placement(
    objects: List[str],
    positions: Optional[List[Tuple[float, float, float]]] = None,
    robot_type: str = "google_robot_static",
    camera_type: str = "overhead_camera",
    lighting_mode: str = "indoor_bright",
    output_dir: str = "/home/<USER>/claude/SpatialVLA/Z/Z_new_trial/result",
    verbose: bool = True
) -> Tuple[bool, Dict, List[str]]:
    """
    🔍 调试物体放置问题 - 专门用于排查穿透和掉落问题

    根据SimplerEnv作者的指导，这个函数会：
    1. 启用调试渲染模式 (rgb_overlay_path=None)
    2. 显示原始sim背景，便于检查物体穿透
    3. 提供详细的物体位置信息
    4. 生成对比图像（正常渲染 vs 调试渲染）

    参数:
        objects: 物体列表
        positions: 物体位置列表，如果为None则使用默认安全位置
        robot_type: 机器人类型
        camera_type: 相机类型
        lighting_mode: 光照模式
        output_dir: 输出目录
        verbose: 是否显示详细信息

    返回:
        (success, results, image_paths)

    💡 使用建议:
        - 如果物体经常掉落或位置异常，使用此函数进行调试
        - 检查生成的调试图像，查看是否有物体穿透问题
        - 对比正常渲染和调试渲染的差异
    """
    
    if verbose:
        print("🔍 SimplerEnv调试工具 - 物体放置问题排查")
        print("=" * 60)
        print("📝 根据作者指导，此工具将:")
        print("   1. 启用调试渲染模式")
        print("   2. 显示原始sim背景")
        print("   3. 检查物体穿透问题")
        print("   4. 生成对比图像")
        print("=" * 60)

    try:
        # 生成正常渲染图像
        print("\n🎯 步骤1: 生成正常渲染图像")
        success_normal, results_normal, images_normal = create_scene_with_custom_positions(
            objects=objects,
            positions=positions or generate_grid_positions(len(objects)),
            robot_type=robot_type,
            camera_type=camera_type,
            lighting_mode=lighting_mode,
            output_dir=output_dir,
            image_prefix="normal",
            num_episodes=1,
            enable_debug_render=False,
            verbose=verbose
        )

        # 生成调试渲染图像
        print("\n🔍 步骤2: 生成调试渲染图像")
        success_debug, results_debug, images_debug = create_scene_with_custom_positions(
            objects=objects,
            positions=positions or generate_grid_positions(len(objects)),
            robot_type=robot_type,
            camera_type=camera_type,
            lighting_mode=lighting_mode,
            output_dir=output_dir,
            image_prefix="debug",
            num_episodes=1,
            enable_debug_render=True,
            verbose=verbose
        )

        # 分析结果
        print("\n📊 步骤3: 分析对比结果")
        
        analysis = {
            "normal_render": {
                "success": success_normal,
                "results": results_normal,
                "images": images_normal
            },
            "debug_render": {
                "success": success_debug,
                "results": results_debug,
                "images": images_debug
            },
            "comparison": {}
        }

        if success_normal and success_debug:
            # 对比物体位置
            if (results_normal.get("episodes") and results_debug.get("episodes") and
                len(results_normal["episodes"]) > 0 and len(results_debug["episodes"]) > 0):
                
                normal_episode = results_normal["episodes"][0]
                debug_episode = results_debug["episodes"][0]
                
                normal_on_table = normal_episode.get("on_table_count", 0)
                debug_on_table = debug_episode.get("on_table_count", 0)
                
                normal_below_table = normal_episode.get("below_table_count", 0)
                debug_below_table = debug_episode.get("below_table_count", 0)
                
                analysis["comparison"] = {
                    "normal_on_table": normal_on_table,
                    "debug_on_table": debug_on_table,
                    "normal_below_table": normal_below_table,
                    "debug_below_table": debug_below_table,
                    "position_consistent": normal_on_table == debug_on_table and normal_below_table == debug_below_table
                }

                if verbose:
                    print(f"   🔍 正常渲染: 桌面上={normal_on_table}, 掉落={normal_below_table}")
                    print(f"   🔍 调试渲染: 桌面上={debug_on_table}, 掉落={debug_below_table}")
                    
                    if analysis["comparison"]["position_consistent"]:
                        print(f"   ✅ 物体位置一致")
                    else:
                        print(f"   ❌ 物体位置不一致，可能存在渲染相关问题")

        # 生成报告
        print(f"\n📋 调试报告:")
        print(f"   🖼️ 正常渲染图像: {len(images_normal)} 张")
        print(f"   🖼️ 调试渲染图像: {len(images_debug)} 张")
        print(f"   📁 图像保存位置: {output_dir}/")
        print(f"   💡 调试提示:")
        print(f"      - 查看调试图像中的物体是否有穿透桌面的情况")
        print(f"      - 对比正常渲染和调试渲染的差异")
        print(f"      - 如果物体掉落，检查初始位置是否合理")

        all_images = images_normal + images_debug
        overall_success = success_normal and success_debug
        
        return overall_success, analysis, all_images

    except Exception as e:
        if verbose:
            print(f"❌ 调试工具执行失败: {e}")
        return False, {"error": str(e)}, []


def diagnose_penetration_issues(
    target_object: str = "apple",
    problematic_objects: List[str] = ["blue_plastic_bottle", "opened_coke_can"],
    output_dir: str = "/home/<USER>/claude/SpatialVLA/Z/Z_new_trial/result",
    verbose: bool = True
) -> Dict:
    """
    🔬 诊断穿透问题 - 专门测试容易出现穿透的物体

    参数:
        target_object: 目标物体（通常选择稳定的物体）
        problematic_objects: 容易出现问题的物体列表
        output_dir: 输出目录
        verbose: 是否显示详细信息

    返回:
        诊断结果字典
    """
    
    if verbose:
        print("🔬 SimplerEnv穿透问题诊断")
        print("=" * 50)
    
    diagnosis = {
        "target_object": target_object,
        "problematic_objects": problematic_objects,
        "test_results": {},
        "recommendations": []
    }

    for obj in problematic_objects:
        if verbose:
            print(f"\n🧪 测试物体: {obj}")
        
        # 测试单个物体
        success, results, images = debug_object_placement(
            objects=[target_object, obj],
            robot_type="google_robot_static",
            camera_type="overhead_camera",
            lighting_mode="indoor_bright",
            output_dir=output_dir,
            verbose=False
        )
        
        test_result = {
            "success": success,
            "results": results,
            "images": images
        }
        
        # 分析结果
        if success and results.get("debug_render", {}).get("results", {}).get("episodes"):
            episode = results["debug_render"]["results"]["episodes"][0]
            below_table = episode.get("below_table_count", 0)
            
            if below_table > 0:
                test_result["has_penetration"] = True
                test_result["penetration_count"] = below_table
                diagnosis["recommendations"].append(f"物体 {obj} 容易穿透，建议调整位置或检查模型")
            else:
                test_result["has_penetration"] = False
                test_result["penetration_count"] = 0
        else:
            test_result["has_penetration"] = None
            test_result["penetration_count"] = None
            diagnosis["recommendations"].append(f"物体 {obj} 测试失败，需要进一步检查")
        
        diagnosis["test_results"][obj] = test_result
        
        if verbose:
            status = "❌ 有穿透" if test_result.get("has_penetration") else "✅ 正常"
            print(f"   结果: {status}")

    if verbose:
        print(f"\n📋 诊断总结:")
        penetration_count = sum(1 for r in diagnosis["test_results"].values() if r.get("has_penetration"))
        print(f"   🔍 测试物体数量: {len(problematic_objects)}")
        print(f"   ❌ 有穿透问题: {penetration_count}")
        print(f"   ✅ 正常物体: {len(problematic_objects) - penetration_count}")
        
        if diagnosis["recommendations"]:
            print(f"   💡 建议:")
            for rec in diagnosis["recommendations"]:
                print(f"      - {rec}")

    return diagnosis


# ==================== 主函数（原有内容保持不变） ====================

if __name__ == "__main__":
    print("🚀 SimplerEnv增强接口演示")
    print("=" * 60)

    # 🎯 简单接口示例 - 一键生成
    print("\n🎯 简单接口示例 - 一键生成场景")
    print("-" * 40)
    success, results, images = create_scene_simple(
        preset="desktop_picking",
        num_episodes=1,
        verbose=True
    )

    if success:
        print("✅ 简单接口测试成功！")
    else:
        print("❌ 简单接口测试失败")

    print("\n" + "="*60)

    # 🎛️ 中级接口示例 - 自定义配置
    print("\n🎛️ 中级接口示例 - 自定义配置")
    print("-" * 40)
    success, results, images = create_scene_custom(
        robot_type="google_robot_static",
        camera_type="overhead_camera",
        lighting_mode="laboratory",
        objects=["apple", "orange", "coke_can", "green_cube_3cm"],
        num_episodes=1,
        verbose=True
    )

    if success:
        print("✅ 中级接口测试成功！")
    else:
        print("❌ 中级接口测试失败")

    print("\n" + "="*60)

    # 🔧 高级接口示例 - 完全自定义
    print("\n🔧 高级接口示例 - 完全自定义")
    print("-" * 40)
    success, results, images = create_scene_advanced({
        "target_object": "orange",
        "distractor_objects": ["pepsi_can", "yellow_cube_3cm", "sponge"],
        "robot_type": "google_robot_static",
        "camera_type": "overhead_camera",
        "lighting_mode": "industrial",
        "workspace_bounds": ((-0.28, -0.22), (0.12, 0.18)),
        "num_episodes": 1,
        "image_prefix": "advanced_demo",
        "verbose": True
    })

    if success:
        print("✅ 高级接口测试成功！")
    else:
        print("❌ 高级接口测试失败")

    print("\n" + "="*60)

    # 🎯 自定义位置接口示例
    print("\n🎯 自定义位置接口示例")
    print("-" * 40)

    # 示例1：手动指定位置
    objects = ["apple", "orange", "coke_can"]
    custom_positions = [
        (-0.25, 0.15, 0),    # apple在中心
        (-0.22, 0.18, 0),    # orange在右上
        (-0.28, 0.12, 0)     # coke_can在左下
    ]

    print("📍 手动指定位置:")
    success, results, images = create_scene_with_custom_positions(
        objects=objects,
        positions=custom_positions,
        image_prefix="manual_positions",
        num_episodes=1,
        verbose=True
    )

    if success:
        print("✅ 自定义位置测试成功！")
    else:
        print("❌ 自定义位置测试失败")

    print("\n" + "-"*40)

    # 示例2：网格布局
    print("📐 网格布局生成:")
    grid_positions = generate_grid_positions(4)
    print(f"生成的网格位置: {grid_positions}")

    success, results, images = create_scene_with_custom_positions(
        objects=["apple", "orange", "coke_can", "green_cube_3cm"],
        positions=grid_positions,
        image_prefix="grid_layout",
        num_episodes=1,
        verbose=True
    )

    if success:
        print("✅ 网格布局测试成功！")
    else:
        print("❌ 网格布局测试失败")

    print("\n" + "-"*40)

    # 示例3：圆形布局
    print("📍 圆形布局生成:")
    circle_positions = generate_circle_positions(3, center=(-0.25, 0.20), radius=0.06)
    print(f"生成的圆形位置: {circle_positions}")

    success, results, images = create_scene_with_custom_positions(
        objects=["apple", "orange", "coke_can"],
        positions=circle_positions,
        image_prefix="circle_layout",
        num_episodes=1,
        verbose=True
    )

    if success:
        print("✅ 圆形布局测试成功！")
    else:
        print("❌ 圆形布局测试失败")

    print("\n" + "="*60)

    # 🔍 调试工具示例
    print("\n🔍 调试工具示例 - 根据作者指导")
    print("-" * 40)
    
    # 示例1：调试物体放置问题
    print("🔍 示例1: 调试物体放置问题")
    debug_success, debug_results, debug_images = debug_object_placement(
        objects=["apple", "blue_plastic_bottle", "opened_coke_can"],
        robot_type="google_robot_static",
        camera_type="overhead_camera",
        lighting_mode="indoor_bright",
        output_dir="/home/<USER>/claude/SpatialVLA/Z/Z_new_trial",
        verbose=True
    )

    if debug_success:
        print("✅ 调试工具测试成功！")
        print(f"   🖼️ 生成对比图像: {len(debug_images)} 张")
        print(f"   📁 请查看输出目录中的 normal_* 和 debug_* 图像")
    else:
        print("❌ 调试工具测试失败")

    print("\n" + "-"*40)

    # 示例2：启用调试渲染的简单接口
    print("🔍 示例2: 启用调试渲染的简单接口")
    success, results, images = create_scene_simple(
        preset="desktop_picking",
        objects=["apple", "blue_plastic_bottle"],
        num_episodes=1,
        enable_debug_render=True,  # 🔧 启用调试渲染
        verbose=True
    )

    if success:
        print("✅ 调试渲染测试成功！")
        print(f"   💡 检查生成的图像，文件名包含 '_debug' 后缀")
    else:
        print("❌ 调试渲染测试失败")

    print("\n" + "-"*40)

    # 示例3：穿透问题诊断
    print("🔬 示例3: 穿透问题诊断")
    diagnosis = diagnose_penetration_issues(
        target_object="apple",
        problematic_objects=["blue_plastic_bottle", "opened_coke_can", "opened_pepsi_can"],
        output_dir="/home/<USER>/claude/SpatialVLA/Z/Z_new_trial",
        verbose=True
    )

    print(f"✅ 穿透问题诊断完成！")
    print(f"   📊 测试了 {len(diagnosis['problematic_objects'])} 个物体")
    print(f"   💡 建议数量: {len(diagnosis['recommendations'])}")

    print("\n" + "="*60)

    # 📋 显示可用配置
    print("\n📋 可用配置选项:")
    print("-" * 40)

    print("🎯 预设配置:")
    presets = SceneConfigManager.list_presets()
    for preset, desc in presets.items():
        print(f"   • {preset}: {desc}")

    print("\n🔆 光照模式:")
    for mode, config in LIGHTING_CONFIGS.items():
        print(f"   • {mode}: {config['description']}")

    print("\n🎲 可用物体:")
    for obj, info in AVAILABLE_OBJECTS.items():
        print(f"   • {obj} ({info['category']}, {info['size']}, {info['difficulty']})")

    print("\n🎊 演示完成！")
    print("💡 使用说明:")
    print("   🚀 新手用户: 使用 create_scene_simple() 一键生成")
    print("   🎛️ 有经验用户: 使用 create_scene_custom() 自定义配置")
    print("   🔧 专家用户: 使用 create_scene_advanced() 完全控制")
    print("   🎯 精确控制: 使用 create_scene_with_custom_positions() 指定物体位置")
    print("   📐 布局生成: 使用 generate_grid_positions() 或 generate_circle_positions()")
    print("   🔍 调试工具: 使用 debug_object_placement() 和 diagnose_penetration_issues()")
    print("   📖 详细文档: 查看函数docstring获取完整参数说明")
    
    print("\n🔍 调试功能说明:")
    print("   • enable_debug_render=True: 启用调试渲染，显示原始sim背景")
    print("   • debug_object_placement(): 对比正常渲染和调试渲染")
    print("   • diagnose_penetration_issues(): 批量测试容易穿透的物体")
    print("   • 根据SimplerEnv作者指导，使用 rgb_overlay_path=None 检查穿透问题")
    
    print("\n📝 调试步骤建议:")
    print("   1. 发现物体掉落或位置异常时，首先启用 enable_debug_render=True")
    print("   2. 使用 debug_object_placement() 生成对比图像")
    print("   3. 查看调试图像中的物体是否穿透桌面")
    print("   4. 根据穿透情况调整物体位置或模型")
    print("   5. 使用 diagnose_penetration_issues() 批量测试问题物体")
