#!/usr/bin/env python3
"""
SimplerEnv调试功能测试脚本

根据SimplerEnv作者的指导，测试调试渲染功能和穿透问题诊断
作者指导：https://github.com/simpler-env/SimplerEnv/issues/111#issuecomment-3070794813

主要功能：
1. 测试调试渲染模式 (rgb_overlay_path=None)
2. 对比正常渲染和调试渲染
3. 检查物体穿透问题
4. 生成详细的诊断报告

使用方法：
    python test_debug_features.py
"""

import os
import sys
from pathlib import Path

# 添加当前目录到路径
sys.path.append(str(Path(__file__).parent))

from simpler_env_interface import (
    debug_object_placement,
    diagnose_penetration_issues,
    create_scene_simple,
    create_scene_with_custom_positions,
    AVAILABLE_OBJECTS
)


def test_debug_rendering():
    """测试调试渲染功能"""
    print("🔍 测试1: 调试渲染功能")
    print("=" * 50)
    
    # 测试容易出现穿透的物体 - 增加物体数量
    problematic_objects = ["apple", "orange", "coke_can", "pepsi_can", "sprite_can", "blue_plastic_bottle", "opened_coke_can", "green_cube_3cm", "yellow_cube_3cm", "sponge"]
    
    print(f"🧪 测试物体: {problematic_objects}")
    print(f"📝 根据作者指导，将启用调试渲染模式")
    
    success, results, images = debug_object_placement(
        objects=problematic_objects,
        robot_type="google_robot_static",
        camera_type="overhead_camera",
        lighting_mode="indoor_bright",
        output_dir="/home/<USER>/claude/SpatialVLA/Z/Z_new_trial/result",
        verbose=True
    )
    
    if success:
        print("✅ 调试渲染测试成功！")
        print(f"   🖼️ 生成图像: {len(images)} 张")
        print(f"   📁 图像位置: /home/<USER>/claude/SpatialVLA/Z/Z_new_trial/")
        print(f"   💡 请查看 normal_* 和 debug_* 图像的差异")
        
        # 分析结果
        if "comparison" in results:
            comp = results["comparison"]
            print(f"   📊 对比结果:")
            print(f"      正常渲染: 桌面上={comp.get('normal_on_table', 0)}, 掉落={comp.get('normal_below_table', 0)}")
            print(f"      调试渲染: 桌面上={comp.get('debug_on_table', 0)}, 掉落={comp.get('debug_below_table', 0)}")
            
            if comp.get('position_consistent', False):
                print(f"      ✅ 物体位置一致")
            else:
                print(f"      ❌ 物体位置不一致，可能存在穿透问题")
    else:
        print("❌ 调试渲染测试失败")
        print(f"   错误信息: {results.get('error', '未知错误')}")
    
    return success, results, images


def test_penetration_diagnosis():
    """测试穿透问题诊断"""
    print("\n🔬 测试2: 穿透问题诊断")
    print("=" * 50)
    
    # 选择一些容易出现穿透的物体 - 增加物体数量
    problematic_objects = [
        "blue_plastic_bottle",      # 轻质塑料瓶
        "opened_coke_can",         # 开罐后的饮料罐
        "opened_pepsi_can",        # 开罐后的饮料罐
        "opened_sprite_can",       # 开罐后的饮料罐
        "opened_7up_can",          # 开罐后的饮料罐
        "opened_fanta_can",        # 开罐后的饮料罐
        "sponge",                  # 海绵（轻质）
        "bridge_carrot_generated_modified",  # 胡萝卜模型
        "eggplant"                 # 茄子模型
    ]
    
    print(f"🧪 测试物体: {problematic_objects}")
    print(f"📝 将逐个测试每个物体的穿透情况")
    
    diagnosis = diagnose_penetration_issues(
        target_object="apple",  # 使用稳定的苹果作为目标物体
        problematic_objects=problematic_objects,
        output_dir="/home/<USER>/claude/SpatialVLA/Z/Z_new_trial/result",
        verbose=True
    )
    
    print(f"✅ 穿透问题诊断完成！")
    
    # 统计结果
    total_objects = len(problematic_objects)
    penetration_count = sum(1 for r in diagnosis['test_results'].values() if r.get('has_penetration'))
    success_count = sum(1 for r in diagnosis['test_results'].values() if r.get('has_penetration') == False)
    
    print(f"📊 诊断统计:")
    print(f"   总测试物体: {total_objects}")
    print(f"   有穿透问题: {penetration_count}")
    print(f"   正常物体: {success_count}")
    print(f"   测试失败: {total_objects - penetration_count - success_count}")
    
    if diagnosis['recommendations']:
        print(f"💡 建议:")
        for rec in diagnosis['recommendations']:
            print(f"   • {rec}")
    
    return diagnosis


def test_simple_interface_with_debug():
    """测试简单接口的调试功能"""
    print("\n🚀 测试3: 简单接口调试功能")
    print("=" * 50)
    
    print("🔍 测试启用调试渲染的简单接口")
    
    success, results, images = create_scene_simple(
        preset="multi_object_sorting",
        objects=["apple", "orange", "coke_can", "pepsi_can", "sprite_can", "blue_plastic_bottle", "opened_coke_can", "green_cube_3cm", "yellow_cube_3cm", "sponge", "fanta_can", "7up_can"],
        num_episodes=1,
        output_dir="/home/<USER>/claude/SpatialVLA/Z/Z_new_trial/result",
        enable_debug_render=True,  # 启用调试渲染
        verbose=True
    )
    
    if success:
        print("✅ 简单接口调试测试成功！")
        print(f"   🖼️ 生成图像: {len(images)} 张")
        
        # 检查是否有调试图像
        debug_images = [img for img in images if '_debug' in img]
        normal_images = [img for img in images if '_debug' not in img]
        
        print(f"   📊 图像统计:")
        print(f"      正常渲染图像: {len(normal_images)} 张")
        print(f"      调试渲染图像: {len(debug_images)} 张")
        
        if debug_images:
            print(f"   🔍 调试图像列表:")
            for img in debug_images:
                print(f"      • {img}")
        
        # 分析结果
        if results.get('episodes'):
            episode = results['episodes'][0]
            print(f"   📊 物体状态:")
            print(f"      桌面上: {episode.get('on_table_count', 0)}")
            print(f"      掉落: {episode.get('below_table_count', 0)}")
            print(f"      成功: {episode.get('success', False)}")
            
            if episode.get('below_table_count', 0) > 0:
                print(f"   ⚠️ 检测到物体掉落，请查看调试图像排查穿透问题")
    else:
        print("❌ 简单接口调试测试失败")
    
    return success, results, images


def test_custom_positions_with_debug():
    """测试自定义位置的调试功能"""
    print("\n🎯 测试4: 自定义位置调试功能")
    print("=" * 50)
    
    # 故意使用一些可能有问题的位置 - 增加物体数量
    objects = ["apple", "orange", "coke_can", "pepsi_can", "sprite_can", "blue_plastic_bottle", "opened_coke_can", "green_cube_3cm", "yellow_cube_3cm", "sponge"]
    
    # 测试不同的位置设置
    test_positions = [
        # 测试1：网格位置
        [(-0.30, 0.10, 0), (-0.25, 0.10, 0), (-0.20, 0.10, 0), (-0.30, 0.15, 0), (-0.25, 0.15, 0), (-0.20, 0.15, 0), (-0.30, 0.20, 0), (-0.25, 0.20, 0), (-0.20, 0.20, 0), (-0.15, 0.20, 0)],
        # 测试2：圆形位置
        [(-0.25, 0.15, 0), (-0.22, 0.18, 0), (-0.19, 0.15, 0), (-0.22, 0.12, 0), (-0.25, 0.12, 0), (-0.28, 0.12, 0), (-0.31, 0.15, 0), (-0.28, 0.18, 0), (-0.25, 0.18, 0), (-0.22, 0.15, 0)],
        # 测试3：随机位置
        [(-0.28, 0.08, 0), (-0.32, 0.22, 0), (-0.18, 0.32, 0), (-0.30, 0.16, 0), (-0.20, 0.24, 0), (-0.26, 0.12, 0), (-0.24, 0.20, 0), (-0.22, 0.14, 0), (-0.28, 0.18, 0), (-0.26, 0.22, 0)]
    ]
    
    for i, positions in enumerate(test_positions):
        print(f"\n📍 测试位置组 {i+1}: {len(positions)} 个位置")
        
        success, results, images = create_scene_with_custom_positions(
            objects=objects,
            positions=positions,
            robot_type="google_robot_static",
            camera_type="overhead_camera",
            lighting_mode="indoor_bright",
            output_dir="/home/<USER>/claude/SpatialVLA/Z/Z_new_trial/result",
            image_prefix=f"custom_test_{i+1}",
            num_episodes=1,
            enable_debug_render=True,  # 启用调试渲染
            verbose=True
        )
        
        if success:
            print(f"   ✅ 测试位置组 {i+1} 成功")
            
            if results.get('episodes'):
                episode = results['episodes'][0]
                on_table = episode.get('on_table_count', 0)
                below_table = episode.get('below_table_count', 0)
                
                print(f"   📊 结果: 桌面上={on_table}, 掉落={below_table}")
                
                if below_table > 0:
                    print(f"   ⚠️ 位置组 {i+1} 有物体掉落，请查看调试图像")
                else:
                    print(f"   ✅ 位置组 {i+1} 所有物体稳定")
        else:
            print(f"   ❌ 测试位置组 {i+1} 失败")


def main():
    """主测试函数"""
    print("🔍 SimplerEnv调试功能测试")
    print("=" * 60)
    print("📝 根据作者指导，测试调试渲染和穿透问题诊断功能")
    print("🔗 参考: https://github.com/simpler-env/SimplerEnv/issues/111#issuecomment-3070794813")
    print("=" * 60)
    
    try:
        # 测试1：调试渲染功能
        test1_success, test1_results, test1_images = test_debug_rendering()
        
        # 测试2：穿透问题诊断
        test2_diagnosis = test_penetration_diagnosis()
        
        # 测试3：简单接口调试功能
        test3_success, test3_results, test3_images = test_simple_interface_with_debug()
        
        # 测试4：自定义位置调试功能
        test_custom_positions_with_debug()
        
        # 总结
        print("\n" + "=" * 60)
        print("📊 测试总结")
        print("=" * 60)
        
        print(f"✅ 测试1 (调试渲染): {'成功' if test1_success else '失败'}")
        print(f"✅ 测试2 (穿透诊断): 完成")
        print(f"✅ 测试3 (简单接口): {'成功' if test3_success else '失败'}")
        print(f"✅ 测试4 (自定义位置): 完成")
        
        all_images = test1_images + test3_images
        print(f"🖼️ 总计生成图像: {len(all_images)} 张")
        print(f"📁 图像保存位置: /home/<USER>/claude/SpatialVLA/Z/Z_new_trial/result/")
        
        print(f"\n💡 使用提示:")
        print(f"   1. 查看生成的图像，对比正常渲染和调试渲染")
        print(f"   2. 调试图像文件名包含 '_debug' 后缀")
        print(f"   3. 如果看到物体穿透桌面，说明存在初始化问题")
        print(f"   4. 根据调试图像调整物体位置或检查模型")
        
        print(f"\n🔍 进一步调试建议:")
        print(f"   • 对于经常掉落的物体，使用 diagnose_penetration_issues() 函数")
        print(f"   • 对于特定场景，使用 debug_object_placement() 函数")
        print(f"   • 在所有接口中都可以使用 enable_debug_render=True 参数")
        
    except Exception as e:
        print(f"❌ 测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main() 